@echo off
setlocal enabledelayedexpansion

REM AI-Powered Video Content Search - Enhanced Windows Installation Script
REM This script provides comprehensive installation with system checks and optimization

title AI-Powered Video Content Search - Installation

echo.
echo ================================================================================
echo  🎬 AI-Powered Video Content Search - Enhanced Windows Installation
echo ================================================================================
echo.

REM Set color codes for output
set "GREEN=[92m"
set "RED=[91m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "RESET=[0m"

REM Initialize variables
set "INSTALL_DIR=%CD%"
set "VENV_DIR=%INSTALL_DIR%\venv"
set "LOG_FILE=%INSTALL_DIR%\install.log"
set "ERROR_COUNT=0"

REM Create log file
echo Installation started at %DATE% %TIME% > "%LOG_FILE%"

echo %BLUE%🔍 Performing system checks...%RESET%

REM Check if running as administrator
net session >nul 2>&1
if %errorlevel% == 0 (
    echo %GREEN%✅ Running with administrator privileges%RESET%
    set "IS_ADMIN=1"
) else (
    echo %YELLOW%⚠️  Not running as administrator - some features may be limited%RESET%
    set "IS_ADMIN=0"
)

REM Check Windows version
for /f "tokens=4-5 delims=. " %%i in ('ver') do set VERSION=%%i.%%j
echo %GREEN%✅ Windows version: %VERSION%%RESET%

REM Check if Python is installed
echo %BLUE%🐍 Checking Python installation...%RESET%
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo %RED%❌ Python is not installed or not in PATH%RESET%
    echo.
    echo Please install Python 3.8+ from https://python.org
    echo Make sure to check "Add Python to PATH" during installation
    echo.
    if "%IS_ADMIN%"=="1" (
        echo Would you like to download Python installer? (y/N)
        set /p DOWNLOAD_PYTHON=
        if /i "!DOWNLOAD_PYTHON!"=="y" (
            call :download_python
        )
    )
    pause
    exit /b 1
)

REM Check Python version
for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
echo %GREEN%✅ Found Python %PYTHON_VERSION%%RESET%

REM Validate Python version (check if 3.8+)
for /f "tokens=1,2 delims=." %%a in ("%PYTHON_VERSION%") do (
    set MAJOR=%%a
    set MINOR=%%b
)
if %MAJOR% LSS 3 (
    echo %RED%❌ Python 3.8+ required, found %PYTHON_VERSION%%RESET%
    pause
    exit /b 1
)
if %MAJOR% EQU 3 if %MINOR% LSS 8 (
    echo %RED%❌ Python 3.8+ required, found %PYTHON_VERSION%%RESET%
    pause
    exit /b 1
)

REM Check if pip is available
echo %BLUE%📦 Checking pip...%RESET%
pip --version >nul 2>&1
if %errorlevel% neq 0 (
    echo %RED%❌ pip is not available%RESET%
    echo Please ensure pip is installed with Python
    pause
    exit /b 1
)
echo %GREEN%✅ pip is available%RESET%

REM Check system dependencies
echo %BLUE%🔧 Checking system dependencies...%RESET%
call :check_dependency git "Git version control"
call :check_dependency ffmpeg "FFmpeg multimedia framework"

REM Check for GPU support
echo %BLUE%🎮 Checking GPU support...%RESET%
nvidia-smi >nul 2>&1
if %errorlevel% == 0 (
    echo %GREEN%✅ NVIDIA GPU detected%RESET%
    set "HAS_NVIDIA=1"
) else (
    echo %YELLOW%⚠️  No NVIDIA GPU detected%RESET%
    set "HAS_NVIDIA=0"
)

REM Ask about virtual environment
echo.
echo %BLUE%🏗️  Virtual Environment Setup%RESET%
if exist "%VENV_DIR%" (
    echo %GREEN%✅ Virtual environment already exists%RESET%
    set "USE_VENV=1"
) else (
    echo Would you like to create a virtual environment? (Y/n)
    set /p CREATE_VENV=
    if /i "!CREATE_VENV!"=="n" (
        set "USE_VENV=0"
    ) else (
        set "USE_VENV=1"
        echo %BLUE%Creating virtual environment...%RESET%
        python -m venv "%VENV_DIR%"
        if !errorlevel! neq 0 (
            echo %RED%❌ Failed to create virtual environment%RESET%
            set /a ERROR_COUNT+=1
            set "USE_VENV=0"
        ) else (
            echo %GREEN%✅ Virtual environment created%RESET%
        )
    )
)

REM Activate virtual environment if using one
if "%USE_VENV%"=="1" (
    echo %BLUE%Activating virtual environment...%RESET%
    call "%VENV_DIR%\Scripts\activate.bat"
    if !errorlevel! neq 0 (
        echo %RED%❌ Failed to activate virtual environment%RESET%
        set /a ERROR_COUNT+=1
    ) else (
        echo %GREEN%✅ Virtual environment activated%RESET%
    )
)

echo.
echo %BLUE%📦 Installing Python dependencies...%RESET%
echo This may take several minutes...
echo.

REM Generate optimized requirements
echo %BLUE%Generating optimized requirements...%RESET%
python requirements_manager.py --gpu --output requirements_optimized.txt >> "%LOG_FILE%" 2>&1
if %errorlevel% == 0 (
    echo %GREEN%✅ Optimized requirements generated%RESET%
    set "REQ_FILE=requirements_optimized.txt"
) else (
    echo %YELLOW%⚠️  Using default requirements%RESET%
    set "REQ_FILE=requirements.txt"
)

REM Upgrade pip first
echo %BLUE%Upgrading pip...%RESET%
python -m pip install --upgrade pip >> "%LOG_FILE%" 2>&1
if %errorlevel% neq 0 (
    echo %YELLOW%⚠️  pip upgrade failed, continuing...%RESET%
    set /a ERROR_COUNT+=1
)

REM Install requirements
echo %BLUE%Installing requirements from %REQ_FILE%...%RESET%
pip install -r "%REQ_FILE%" >> "%LOG_FILE%" 2>&1
if %errorlevel% neq 0 (
    echo %RED%❌ Failed to install Python dependencies%RESET%
    echo Check %LOG_FILE% for details
    echo Please check your internet connection and try again
    set /a ERROR_COUNT+=1

    REM Try with basic requirements as fallback
    if exist "requirements.txt" (
        echo %YELLOW%Trying with basic requirements...%RESET%
        pip install -r requirements.txt >> "%LOG_FILE%" 2>&1
        if !errorlevel! neq 0 (
            echo %RED%❌ Basic requirements installation also failed%RESET%
            pause
            exit /b 1
        ) else (
            echo %GREEN%✅ Basic requirements installed%RESET%
        )
    )
) else (
    echo %GREEN%✅ All dependencies installed successfully%RESET%
)

echo.
echo %BLUE%📁 Creating directories...%RESET%
call :create_directory "static"
call :create_directory "static\output_clips"
call :create_directory "temp_videos"
call :create_directory "test_output"
call :create_directory "models\cache"
call :create_directory "logs"
call :create_directory "data"

echo.
echo %BLUE%🔧 Running advanced installation script...%RESET%
python install_manager.py >> "%LOG_FILE%" 2>&1
if %errorlevel% neq 0 (
    echo %YELLOW%⚠️  Advanced installation script encountered issues%RESET%
    echo Running basic installation script...
    python install.py >> "%LOG_FILE%" 2>&1
    if !errorlevel! neq 0 (
        echo %YELLOW%⚠️  Installation script encountered issues%RESET%
        echo The application may still work, but some features might be limited
        set /a ERROR_COUNT+=1
    )
) else (
    echo %GREEN%✅ Advanced installation completed%RESET%
)

echo.
echo %BLUE%🚀 Creating launcher scripts...%RESET%
call :create_launcher_script "launch_web.bat" "Web Interface" "python main.py --web"
call :create_launcher_script "launch_live.bat" "Live Detection" "python main.py --live --query"
call :create_launcher_script "launch_test.bat" "Application Test" "python test_application.py"

echo.
echo %BLUE%🧪 Running application tests...%RESET%
python test_application.py >> "%LOG_FILE%" 2>&1
if %errorlevel% neq 0 (
    echo %YELLOW%⚠️  Some tests failed%RESET%
    echo The application may still work, but with limited functionality
    set /a ERROR_COUNT+=1
) else (
    echo %GREEN%✅ All tests passed%RESET%
)

echo.
echo %BLUE%📝 Creating documentation shortcuts...%RESET%
call :create_doc_shortcut "README.md" "Main Documentation"
call :create_doc_shortcut "docs\COMPLETE_USER_GUIDE.md" "Complete User Guide"

echo.
REM Final summary
if %ERROR_COUNT% == 0 (
    echo %GREEN%========================================================================%RESET%
    echo %GREEN% 🎉 Installation Completed Successfully!%RESET%
    echo %GREEN%========================================================================%RESET%
) else (
    echo %YELLOW%========================================================================%RESET%
    echo %YELLOW% ⚠️  Installation Completed with %ERROR_COUNT% warnings%RESET%
    echo %YELLOW%========================================================================%RESET%
)
echo.
echo %BLUE%🚀 Next Steps:%RESET%
echo   1. Test the installation: launch_test.bat
echo   2. Start web interface: launch_web.bat
echo   3. Try live detection: launch_live.bat
echo   4. Command line usage: python main.py --video video.mp4 --query "red car"
echo   5. Read documentation: README.md
echo.
echo %BLUE%📁 Installation log saved to: %LOG_FILE%%RESET%
echo.
pause
goto :eof

REM Helper Functions
:check_dependency
where %1 >nul 2>&1
if %errorlevel% == 0 (
    echo %GREEN%✅ %1%RESET% - %~2
) else (
    echo %RED%❌ %1%RESET% - %~2
    echo    Please install %1 manually
    set /a ERROR_COUNT+=1
)
goto :eof

:create_directory
if not exist "%~1" (
    mkdir "%~1" >nul 2>&1
    if %errorlevel% == 0 (
        echo %GREEN%✅ Created directory: %~1%RESET%
    ) else (
        echo %RED%❌ Failed to create directory: %~1%RESET%
        set /a ERROR_COUNT+=1
    )
) else (
    echo %GREEN%✅ Directory exists: %~1%RESET%
)
goto :eof

:create_launcher_script
set "SCRIPT_NAME=%~1"
set "SCRIPT_DESC=%~2"
set "SCRIPT_CMD=%~3"

echo @echo off > "%SCRIPT_NAME%"
echo title AI Video Search - %SCRIPT_DESC% >> "%SCRIPT_NAME%"
echo echo. >> "%SCRIPT_NAME%"
echo echo ======================================== >> "%SCRIPT_NAME%"
echo echo  AI Video Search - %SCRIPT_DESC% >> "%SCRIPT_NAME%"
echo echo ======================================== >> "%SCRIPT_NAME%"
echo echo. >> "%SCRIPT_NAME%"

if "%SCRIPT_NAME%"=="launch_live.bat" (
    echo set /p query="Enter search query (e.g., 'person', 'car'): " >> "%SCRIPT_NAME%"
    echo if "%%query%%"=="" set query=person >> "%SCRIPT_NAME%"
    echo echo Starting live detection for: %%query%% >> "%SCRIPT_NAME%"
    echo %SCRIPT_CMD% "%%query%%" >> "%SCRIPT_NAME%"
) else (
    echo echo Starting %SCRIPT_DESC%... >> "%SCRIPT_NAME%"
    echo %SCRIPT_CMD% >> "%SCRIPT_NAME%"
)

echo echo. >> "%SCRIPT_NAME%"
echo if %%errorlevel%% neq 0 ( >> "%SCRIPT_NAME%"
echo     echo ERROR: %SCRIPT_DESC% failed to start >> "%SCRIPT_NAME%"
echo     echo Check the installation and try again >> "%SCRIPT_NAME%"
echo ^) >> "%SCRIPT_NAME%"
echo pause >> "%SCRIPT_NAME%"

echo %GREEN%✅ Created launcher: %SCRIPT_NAME%%RESET%
goto :eof

:create_doc_shortcut
if exist "%~1" (
    echo %GREEN%✅ Documentation available: %~2%RESET%
) else (
    echo %YELLOW%⚠️  Documentation not found: %~1%RESET%
)
goto :eof

:download_python
echo %BLUE%Downloading Python installer...%RESET%
powershell -Command "Invoke-WebRequest -Uri 'https://www.python.org/ftp/python/3.11.0/python-3.11.0-amd64.exe' -OutFile 'python-installer.exe'"
if %errorlevel% == 0 (
    echo %GREEN%✅ Python installer downloaded%RESET%
    echo %BLUE%Starting Python installation...%RESET%
    python-installer.exe /quiet InstallAllUsers=1 PrependPath=1
    echo %GREEN%✅ Python installation completed%RESET%
    echo Please restart this script after Python installation
) else (
    echo %RED%❌ Failed to download Python installer%RESET%
)
goto :eof
