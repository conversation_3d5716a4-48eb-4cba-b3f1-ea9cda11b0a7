@echo off
setlocal enabledelayedexpansion

REM AI-Powered Video Content Search - Enhanced Windows Installation Script
REM This script provides comprehensive installation with system checks and optimization

title AI-Powered Video Content Search - Installation

echo.
echo ================================================================================
echo  🎬 AI-Powered Video Content Search - Enhanced Windows Installation
echo ================================================================================
echo.

REM Set color codes for output
set "GREEN=[92m"
set "RED=[91m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "RESET=[0m"

REM Initialize variables
set "INSTALL_DIR=%CD%"
set "VENV_DIR=%INSTALL_DIR%\venv"
set "LOG_FILE=%INSTALL_DIR%\install.log"
set "ERROR_COUNT=0"

REM Create log file
echo Installation started at %DATE% %TIME% > "%LOG_FILE%"

echo %BLUE%🔍 Performing system checks...%RESET%

REM Check if running as administrator
net session >nul 2>&1
if %errorlevel% == 0 (
    echo %GREEN%✅ Running with administrator privileges%RESET%
    set "IS_ADMIN=1"
) else (
    echo %YELLOW%⚠️  Not running as administrator - some features may be limited%RESET%
    set "IS_ADMIN=0"
)

REM Check Windows version
for /f "tokens=4-5 delims=. " %%i in ('ver') do set VERSION=%%i.%%j
echo %GREEN%✅ Windows version: %VERSION%%RESET%

REM Check if Python is installed
echo %BLUE%🐍 Checking Python installation...%RESET%
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo %RED%❌ Python is not installed or not in PATH%RESET%
    echo.
    echo Please install Python 3.8+ from https://python.org
    echo Make sure to check "Add Python to PATH" during installation
    echo.
    if "%IS_ADMIN%"=="1" (
        echo Would you like to download Python installer? (y/N)
        set /p DOWNLOAD_PYTHON=
        if /i "!DOWNLOAD_PYTHON!"=="y" (
            call :download_python
        )
    )
    pause
    exit /b 1
)

REM Check Python version
for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
echo %GREEN%✅ Found Python %PYTHON_VERSION%%RESET%

REM Validate Python version (check if 3.8+)
for /f "tokens=1,2 delims=." %%a in ("%PYTHON_VERSION%") do (
    set MAJOR=%%a
    set MINOR=%%b
)
if %MAJOR% LSS 3 (
    echo %RED%❌ Python 3.8+ required, found %PYTHON_VERSION%%RESET%
    pause
    exit /b 1
)
if %MAJOR% EQU 3 if %MINOR% LSS 8 (
    echo %RED%❌ Python 3.8+ required, found %PYTHON_VERSION%%RESET%
    pause
    exit /b 1
)

REM Check if pip is available
echo %BLUE%📦 Checking pip...%RESET%
pip --version >nul 2>&1
if %errorlevel% neq 0 (
    echo %RED%❌ pip is not available%RESET%
    echo Please ensure pip is installed with Python
    pause
    exit /b 1
)
echo %GREEN%✅ pip is available%RESET%

REM Check system dependencies
echo %BLUE%🔧 Checking system dependencies...%RESET%
call :check_dependency git "Git version control"
call :check_dependency ffmpeg "FFmpeg multimedia framework"

REM Check for GPU support
echo %BLUE%🎮 Checking GPU support...%RESET%
nvidia-smi >nul 2>&1
if %errorlevel% == 0 (
    echo %GREEN%✅ NVIDIA GPU detected%RESET%
    set "HAS_NVIDIA=1"
) else (
    echo %YELLOW%⚠️  No NVIDIA GPU detected%RESET%
    set "HAS_NVIDIA=0"
)

REM Ask about virtual environment
echo.
echo %BLUE%🏗️  Virtual Environment Setup%RESET%
if exist "%VENV_DIR%" (
    echo %GREEN%✅ Virtual environment already exists%RESET%
    set "USE_VENV=1"
) else (
    echo Would you like to create a virtual environment? (Y/n)
    set /p CREATE_VENV=
    if /i "!CREATE_VENV!"=="n" (
        set "USE_VENV=0"
    ) else (
        set "USE_VENV=1"
        echo %BLUE%Creating virtual environment...%RESET%
        python -m venv "%VENV_DIR%"
        if !errorlevel! neq 0 (
            echo %RED%❌ Failed to create virtual environment%RESET%
            set /a ERROR_COUNT+=1
            set "USE_VENV=0"
        ) else (
            echo %GREEN%✅ Virtual environment created%RESET%
        )
    )
)

REM Activate virtual environment if using one
if "%USE_VENV%"=="1" (
    echo %BLUE%Activating virtual environment...%RESET%
    call "%VENV_DIR%\Scripts\activate.bat"
    if !errorlevel! neq 0 (
        echo %RED%❌ Failed to activate virtual environment%RESET%
        set /a ERROR_COUNT+=1
    ) else (
        echo %GREEN%✅ Virtual environment activated%RESET%
    )
)

echo.
echo %BLUE%📦 Installing Python dependencies...%RESET%
echo This may take several minutes...
echo.

REM Generate optimized requirements
echo %BLUE%Generating optimized requirements...%RESET%
python requirements_manager.py --gpu --output requirements_optimized.txt >> "%LOG_FILE%" 2>&1
if %errorlevel% == 0 (
    echo %GREEN%✅ Optimized requirements generated%RESET%
    set "REQ_FILE=requirements_optimized.txt"
) else (
    echo %YELLOW%⚠️  Using default requirements%RESET%
    set "REQ_FILE=requirements.txt"
)

REM Upgrade pip first
echo %BLUE%Upgrading pip...%RESET%
python -m pip install --upgrade pip >> "%LOG_FILE%" 2>&1
if %errorlevel% neq 0 (
    echo %YELLOW%⚠️  pip upgrade failed, continuing...%RESET%
    set /a ERROR_COUNT+=1
)

REM Install requirements
echo %BLUE%Installing requirements from %REQ_FILE%...%RESET%
pip install -r "%REQ_FILE%" >> "%LOG_FILE%" 2>&1
if %errorlevel% neq 0 (
    echo %RED%❌ Failed to install Python dependencies%RESET%
    echo Check %LOG_FILE% for details
    echo Please check your internet connection and try again
    set /a ERROR_COUNT+=1

    REM Try with basic requirements as fallback
    if exist "requirements.txt" (
        echo %YELLOW%Trying with basic requirements...%RESET%
        pip install -r requirements.txt >> "%LOG_FILE%" 2>&1
        if !errorlevel! neq 0 (
            echo %RED%❌ Basic requirements installation also failed%RESET%
            pause
            exit /b 1
        ) else (
            echo %GREEN%✅ Basic requirements installed%RESET%
        )
    )
) else (
    echo %GREEN%✅ All dependencies installed successfully%RESET%
)

echo.
echo %BLUE%📁 Creating directories...%RESET%
call :create_directory "static"
call :create_directory "static\output_clips"
call :create_directory "temp_videos"
call :create_directory "test_output"
call :create_directory "models\cache"
call :create_directory "logs"
call :create_directory "data"

echo.
echo %BLUE%🔧 Running advanced installation script...%RESET%
python install_manager.py >> "%LOG_FILE%" 2>&1
if %errorlevel% neq 0 (
    echo %YELLOW%⚠️  Advanced installation script encountered issues%RESET%
    echo Running basic installation script...
    python install.py >> "%LOG_FILE%" 2>&1
    if !errorlevel! neq 0 (
        echo %YELLOW%⚠️  Installation script encountered issues%RESET%
        echo The application may still work, but some features might be limited
        set /a ERROR_COUNT+=1
    )
) else (
    echo %GREEN%✅ Advanced installation completed%RESET%
)

echo.
echo Creating launcher scripts...
echo @echo off > launch_web.bat
echo echo Starting AI Video Search Web Interface... >> launch_web.bat
echo python main.py --web >> launch_web.bat
echo pause >> launch_web.bat

echo @echo off > launch_live.bat
echo echo Starting AI Video Search Live Detection... >> launch_live.bat
echo set /p query="Enter search query (e.g., 'person', 'car'): " >> launch_live.bat
echo python main.py --live --query "%%query%%" >> launch_live.bat
echo pause >> launch_live.bat

echo.
echo Running application tests...
python test_application.py
if %errorlevel% neq 0 (
    echo WARNING: Some tests failed
    echo The application may still work, but with limited functionality
)

echo.
echo ========================================================================
echo  Installation Complete!
echo ========================================================================
echo.
echo Next steps:
echo   1. Test the installation: python test_application.py
echo   2. Start web interface: launch_web.bat (or python main.py --web)
echo   3. Try live detection: launch_live.bat (or python main.py --live)
echo   4. Command line usage: python main.py --video video.mp4 --query "red car"
echo.
echo For help and documentation, see README.md
echo.
pause
