# 🚀 Quick Start Guide - AI-Powered Video Content Search

Get up and running with AI-Powered Video Content Search in just 5 minutes! This guide will walk you through installation, first search, and key features.

## ⏱️ 5-Minute Setup

### Step 1: Install (2 minutes)

Choose your preferred installation method:

#### Option A: Automated Installation (Recommended)
```bash
# Windows
git clone https://github.com/yourusername/ai-video-search.git
cd ai-video-search
install.bat

# Linux/macOS
git clone https://github.com/yourusername/ai-video-search.git
cd ai-video-search
chmod +x install.sh
./install.sh
```

#### Option B: Docker (If you have Docker)
```bash
git clone https://github.com/yourusername/ai-video-search.git
cd ai-video-search
chmod +x docker-install.sh
./docker-install.sh
```

### Step 2: Start the Application (30 seconds)

```bash
# Start the web interface
python main.py --web

# Or use the launcher script
./launch_web.sh      # Linux/macOS
launch_web.bat       # Windows
```

Open your browser to: **http://localhost:8501**

### Step 3: Your First Search (2 minutes)

1. **Upload a Video**
   - Click "Browse files" or drag & drop a video file
   - Supported formats: MP4, AVI, MOV, MKV, WMV, FLV, WebM

2. **Enter Search Query**
   - Type what you're looking for: "red car", "person walking", "dog"
   - Be descriptive but concise

3. **Click Search**
   - Wait for processing (usually 30 seconds - 2 minutes)
   - View results with timestamps and similarity scores

4. **Explore Results**
   - Click thumbnails to view full-size images
   - Download video clips of matches
   - Adjust similarity threshold for more/fewer results

## 🎯 Essential Features

### 🔍 **Smart Search Queries**

**Good Examples:**
```
"red sports car"          # Specific object with attributes
"person wearing glasses"  # Person with characteristics
"sunset over water"       # Scene description
"dog running in park"     # Action in context
"blue bicycle"            # Object with color
```

**Tips for Better Results:**
- Be specific: "red car" vs "car"
- Include context: "person in kitchen" vs "person"
- Use descriptive adjectives: "large white dog" vs "dog"
- Try variations if first search doesn't work well

### 📊 **Understanding Results**

**Similarity Scores:**
- **0.3-0.5:** Very high confidence matches
- **0.2-0.3:** Good matches, likely relevant
- **0.1-0.2:** Possible matches, may need review
- **Below 0.1:** Low confidence, likely false positives

**Adjusting Threshold:**
- **Higher threshold (0.3+):** Fewer, more accurate results
- **Lower threshold (0.1-0.2):** More results, some false positives
- **Start with 0.2** and adjust based on results

### ⚙️ **Quick Settings**

**For Faster Processing:**
- Increase "Frame Interval" to 60+ (samples fewer frames)
- Disable "Create Video Clips" (images only)
- Use lower resolution if available

**For Better Accuracy:**
- Decrease "Frame Interval" to 15-30 (samples more frames)
- Enable "Advanced Matching" if available
- Use higher similarity threshold (0.25+)

## 🎬 **Common Use Cases**

### 📹 **Security Footage Analysis**
```bash
# Search for people in security footage
Query: "person"
Threshold: 0.25
Frame Interval: 30

# Search for vehicles
Query: "car" or "vehicle"
Threshold: 0.2
```

### 🎥 **Content Discovery**
```bash
# Find specific scenes in movies/videos
Query: "outdoor scene" or "indoor room"
Query: "crowd of people" or "empty street"
Query: "night scene" or "daytime"
```

### 📱 **Personal Video Organization**
```bash
# Find family moments
Query: "child" or "baby"
Query: "birthday party" or "celebration"
Query: "pet" or "dog" or "cat"
```

### 🏢 **Professional Video Editing**
```bash
# Find B-roll footage
Query: "city skyline" or "nature landscape"
Query: "close-up face" or "wide shot"
Query: "product shot" or "logo"
```

## 🔧 **Command Line Quick Start**

For automation and scripting:

```bash
# Basic search
python main.py --video video.mp4 --query "red car"

# Advanced search with options
python main.py --video video.mp4 --query "person walking" \
  --threshold 0.25 --max-results 10 --create-clips --create-thumbnails

# Live detection from webcam
python main.py --live --query "person" --camera 0

# Batch processing multiple videos
python main.py --batch-videos video1.mp4 video2.mp4 --query "car"
```

## 📱 **Live Detection Quick Start**

### Webcam Detection
1. Go to "Live Video Detection" tab in web interface
2. Select camera (usually Camera 0)
3. Enter search query (e.g., "person", "object")
4. Click "Start Detection"
5. View real-time results

### IP Camera/RTSP Stream
1. Get your camera's RTSP URL (e.g., `rtsp://*************:554/stream`)
2. Enter URL in "Stream URL" field
3. Configure detection settings
4. Start detection

## 🚨 **Quick Troubleshooting**

### **Installation Issues**
```bash
# Test installation
python test_installation.py

# Check Python version (need 3.8+)
python --version

# Check if all packages installed
python -c "import torch, cv2, streamlit; print('All good!')"
```

### **Performance Issues**
```bash
# Check GPU availability
python -c "import torch; print('CUDA available:', torch.cuda.is_available())"

# Reduce memory usage
# - Increase frame interval
# - Disable video clip creation
# - Use lower resolution
```

### **No Results Found**
- Try lower similarity threshold (0.1-0.15)
- Use different query variations
- Check video quality and content
- Ensure video contains what you're searching for

### **Web Interface Won't Start**
```bash
# Check if port is in use
netstat -an | grep 8501

# Try different port
python main.py --web --port 8502
```

## 📚 **Next Steps**

### **Learn More**
- [Complete User Guide](COMPLETE_USER_GUIDE.md) - Comprehensive feature documentation
- [Web Interface Guide](web-interface-guide.md) - Detailed UI walkthrough
- [CLI Reference](cli-reference.md) - All command line options

### **Advanced Features**
- [Live Detection Guide](../LIVE_DETECTION_GUIDE.md) - Real-time processing
- [Batch Processing](batch-processing-guide.md) - Multiple video workflows
- [Performance Optimization](../OPTIMIZATION_SUMMARY.md) - Speed and accuracy tuning

### **Integration**
- [API Reference](API_REFERENCE.md) - Developer documentation
- [Integration Examples](tutorials/integration-examples.md) - Using with other tools

## 💡 **Pro Tips**

### **Search Strategy**
1. **Start broad, then narrow:** Begin with general terms, then add specifics
2. **Try multiple variations:** "car", "vehicle", "automobile"
3. **Use context:** "person in kitchen" vs just "person"
4. **Experiment with thresholds:** Find the sweet spot for your content

### **Performance Optimization**
1. **Use GPU if available:** Significantly faster processing
2. **Adjust frame intervals:** Balance speed vs thoroughness
3. **Process in chunks:** For very large videos
4. **Monitor memory usage:** Close other applications if needed

### **Workflow Efficiency**
1. **Save successful queries:** Note what works for future use
2. **Batch similar videos:** Process related content together
3. **Use appropriate output formats:** Images for quick review, clips for editing
4. **Organize results:** Create folders for different search types

## 🎉 **You're Ready!**

Congratulations! You now have AI-Powered Video Content Search running and know the basics. Start with simple searches and gradually explore more advanced features as you become comfortable with the system.

**Happy searching! 🔍🎬**

---

**Need Help?**
- 📖 [Complete Documentation](README.md)
- 🐛 [Troubleshooting Guide](troubleshooting.md)
- ❓ [FAQ](faq.md)
- 💬 [Community Support](https://github.com/yourusername/ai-video-search/discussions)
