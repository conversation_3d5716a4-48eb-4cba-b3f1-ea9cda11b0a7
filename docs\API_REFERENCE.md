# 🔧 AI-Powered Video Content Search - API Reference

This document provides comprehensive API documentation for developers who want to integrate or extend the AI-Powered Video Content Search system.

## 📋 Table of Contents

1. [Core Classes](#core-classes)
2. [Video Search Engine](#video-search-engine)
3. [Frame Extraction](#frame-extraction)
4. [CLIP Model Integration](#clip-model-integration)
5. [Live Detection](#live-detection)
6. [Object Extraction](#object-extraction)
7. [Configuration System](#configuration-system)
8. [Utility Functions](#utility-functions)
9. [Error Handling](#error-handling)
10. [Examples](#examples)

## 🏗️ Core Classes

### VideoSearchEngine

The main class for video content search functionality.

```python
from utils.clip_match import VideoSearchEngine

class VideoSearchEngine:
    def __init__(self,
                 clip_model_name: str = "openai/clip-vit-base-patch32",
                 output_dir: str = "static/output_clips",
                 frame_interval: int = 30,
                 max_frames: Optional[int] = None,
                 target_resolution: Optional[Tuple[int, int]] = (512, 384),
                 quality_factor: float = 0.8,
                 use_chunked_processing: bool = True,
                 chunk_size: int = 200,
                 adaptive_config: bool = True,
                 enable_parallel_processing: bool = True,
                 max_workers: Optional[int] = None,
                 enable_memory_monitoring: bool = True,
                 memory_limit_mb: Optional[int] = None)
```

**Parameters:**
- `clip_model_name`: CLIP model identifier
- `output_dir`: Directory for output files
- `frame_interval`: Extract every N frames
- `max_frames`: Maximum frames to extract (None for unlimited)
- `target_resolution`: Processing resolution (width, height)
- `quality_factor`: Compression quality (0.1-1.0)
- `use_chunked_processing`: Enable chunked processing for large videos
- `chunk_size`: Frames per chunk
- `adaptive_config`: Use adaptive configuration
- `enable_parallel_processing`: Enable parallel processing
- `max_workers`: Maximum worker threads
- `enable_memory_monitoring`: Enable memory monitoring
- `memory_limit_mb`: Memory limit in MB

#### Methods

##### search_video()

```python
def search_video(self,
                video_path: str,
                query: str,
                similarity_threshold: float = 0.2,
                top_k: Optional[int] = 20,
                create_clips: bool = False,
                clip_duration: float = 3.0,
                create_thumbnails: bool = True,
                thumbnail_size: Tuple[int, int] = (480, 360),
                image_quality: int = 90,
                use_advanced_matching: bool = True,
                progress_callback: Optional[callable] = None) -> Dict[str, Any]
```

**Parameters:**
- `video_path`: Path to video file
- `query`: Search query text
- `similarity_threshold`: Minimum similarity score (0.0-1.0)
- `top_k`: Maximum results to return
- `create_clips`: Generate video clips
- `clip_duration`: Duration of clips in seconds
- `create_thumbnails`: Generate thumbnail images
- `thumbnail_size`: Thumbnail dimensions
- `image_quality`: JPEG quality (1-100)
- `use_advanced_matching`: Use advanced matching algorithms
- `progress_callback`: Progress callback function

**Returns:**
```python
{
    'query': str,
    'video_path': str,
    'video_info': Dict[str, Any],
    'matches': List[Dict[str, Any]],
    'clips': List[str],
    'thumbnails': List[Dict[str, Any]],
    'processing_time': float,
    'stats': Dict[str, Any],
    'success': bool,
    'error': Optional[str]
}
```

##### get_performance_stats()

```python
def get_performance_stats(self) -> Dict[str, Any]
```

**Returns:**
```python
{
    'total_searches': int,
    'total_processing_time': float,
    'average_processing_time': float,
    'cache_hits': int,
    'cache_misses': int,
    'cache_hit_rate': float,
    'error_count': int
}
```

##### get_cache_info()

```python
def get_cache_info(self) -> Dict[str, Any]
```

**Returns:**
```python
{
    'cached_videos': List[str],
    'total_cached_frames': int,
    'memory_usage_mb': float,
    'memory_limit_mb': int,
    'clip_matcher_loaded': bool
}
```

### FrameExtractor

Handles video frame extraction and preprocessing.

```python
from utils.frame_extraction import FrameExtractor

class FrameExtractor:
    def __init__(self,
                 every_n_frames: int = 30,
                 max_frames: Optional[int] = None,
                 target_resolution: Optional[Tuple[int, int]] = None,
                 quality_factor: float = 0.8)
```

#### Methods

##### extract_frames()

```python
def extract_frames(self, video_path: str) -> List[Tuple[int, np.ndarray, float]]
```

**Parameters:**
- `video_path`: Path to video file

**Returns:**
List of tuples containing (frame_index, frame_array, timestamp)

##### get_video_info()

```python
def get_video_info(self, video_path: str) -> Dict[str, Any]
```

**Returns:**
```python
{
    'duration_seconds': float,
    'fps': float,
    'width': int,
    'height': int,
    'total_frames': int,
    'codec': str,
    'bitrate': int,
    'file_size_mb': float
}
```

### CLIPMatcher

Handles CLIP model operations for text-image matching.

```python
from models.clip_model import CLIPMatcher

class CLIPMatcher:
    def __init__(self, model_name: str = "openai/clip-vit-base-patch32")
```

#### Methods

##### search_frames()

```python
def search_frames(self,
                 frames: List[Tuple[int, np.ndarray, float]],
                 query: str,
                 threshold: float = 0.2,
                 top_k: Optional[int] = None,
                 use_advanced_matching: bool = False) -> List[Tuple[int, np.ndarray, float, float]]
```

**Parameters:**
- `frames`: List of frame tuples
- `query`: Search query
- `threshold`: Similarity threshold
- `top_k`: Maximum results
- `use_advanced_matching`: Enable advanced matching

**Returns:**
List of tuples containing (frame_index, frame_array, timestamp, similarity_score)

##### encode_text()

```python
def encode_text(self, text: str) -> np.ndarray
```

##### encode_images()

```python
def encode_images(self, images: List[np.ndarray]) -> np.ndarray
```

### LiveVideoDetector

Handles real-time video detection from cameras and streams.

```python
from utils.live_detection import LiveVideoDetector

class LiveVideoDetector:
    def __init__(self,
                 clip_model_name: str = "openai/clip-vit-base-patch32",
                 detection_interval: float = 0.5,
                 max_detections: int = 20)
```

#### Methods

##### start_camera()

```python
def start_camera(self, camera_index: int = 0) -> bool
```

##### start_stream()

```python
def start_stream(self, stream_url: str) -> bool
```

##### start_detection()

```python
def start_detection(self,
                   query: str,
                   similarity_threshold: float = 0.2,
                   save_detections: bool = True) -> None
```

##### pause_detection()

```python
def pause_detection(self) -> None
```

##### resume_detection()

```python
def resume_detection(self) -> None
```

##### stop_detection()

```python
def stop_detection(self) -> None
```

##### get_detection_stats()

```python
def get_detection_stats(self) -> Dict[str, Any]
```

### ObjectExtractor

Handles object detection and extraction from images.

```python
from utils.object_extraction import ObjectExtractor

class ObjectExtractor:
    def __init__(self, device: Optional[str] = None)
```

#### Methods

##### extract_objects_from_frame()

```python
def extract_objects_from_frame(self,
                              image: np.ndarray,
                              query: str,
                              confidence_threshold: float = 0.5,
                              max_objects: int = 5) -> List[Tuple[np.ndarray, float, str]]
```

**Parameters:**
- `image`: Input image array (RGB)
- `query`: Object query
- `confidence_threshold`: Minimum confidence
- `max_objects`: Maximum objects to extract

**Returns:**
List of tuples containing (cropped_object, confidence, object_class)

## ⚙️ Configuration System

### AdvancedConfig

Comprehensive configuration management.

```python
from config_advanced import AdvancedConfig

class AdvancedConfig:
    def __init__(self, config_file: str = "config.json")
```

#### Properties

```python
config = AdvancedConfig()

# Model settings
config.models.clip_model_name
config.models.yolo_model_name
config.models.device

# Processing settings
config.processing.default_frame_interval
config.processing.default_target_resolution
config.processing.chunk_size

# Performance settings
config.performance.enable_gpu
config.performance.batch_size
config.performance.num_workers

# Memory settings
config.memory.max_cache_size_mb
config.memory.enable_memory_monitoring
```

#### Methods

##### save_config()

```python
def save_config(self, file_path: Optional[str] = None) -> bool
```

##### load_config()

```python
def load_config(self) -> bool
```

## 🔧 Utility Functions

### Video Processing Utilities

```python
from utils.video_slicing import VideoClipper

# Create video clips from matches
clipper = VideoClipper(output_dir="output")
clips = clipper.create_clips_from_matches(
    video_path="video.mp4",
    matches=matches,
    clip_duration=3.0,
    query_name="search_query"
)

# Extract thumbnails
thumbnails = clipper.extract_frame_thumbnails(
    matches=matches,
    thumbnail_size=(480, 360),
    image_quality=90,
    query="search_query",
    extract_objects_only=True
)
```

### Advanced Matching

```python
from utils.advanced_matching import AdvancedMatcher

matcher = AdvancedMatcher(
    confidence_threshold=0.7,
    enable_clustering=True,
    enable_temporal_filtering=True,
    enable_semantic_filtering=True
)

results = matcher.search_frames(
    frames=frames,
    query="search_query",
    threshold=0.2,
    top_k=10
)
```

## ❌ Error Handling

### Exception Types

```python
# Custom exceptions
class VideoSearchError(Exception):
    """Base exception for video search errors"""
    pass

class ModelLoadError(VideoSearchError):
    """Raised when AI models fail to load"""
    pass

class VideoProcessingError(VideoSearchError):
    """Raised when video processing fails"""
    pass

class ConfigurationError(VideoSearchError):
    """Raised when configuration is invalid"""
    pass
```

### Error Handling Patterns

```python
try:
    search_engine = VideoSearchEngine()
    results = search_engine.search_video(
        video_path="video.mp4",
        query="search_query"
    )
    
    if results['success']:
        print(f"Found {len(results['matches'])} matches")
    else:
        print(f"Search failed: {results['error']}")
        
except ModelLoadError as e:
    print(f"Failed to load AI models: {e}")
except VideoProcessingError as e:
    print(f"Video processing failed: {e}")
except Exception as e:
    print(f"Unexpected error: {e}")
```

## 📝 Examples

### Basic Video Search

```python
from utils.clip_match import VideoSearchEngine

# Initialize search engine
engine = VideoSearchEngine(
    clip_model_name="openai/clip-vit-base-patch32",
    frame_interval=30,
    enable_parallel_processing=True
)

# Search video
results = engine.search_video(
    video_path="sample_video.mp4",
    query="red car",
    similarity_threshold=0.25,
    top_k=10,
    create_thumbnails=True
)

# Process results
if results['success']:
    print(f"Processing time: {results['processing_time']:.2f}s")
    print(f"Matches found: {len(results['matches'])}")
    
    for match in results['matches']:
        print(f"Frame {match['frame_index']} at {match['time_formatted']}: "
              f"Score {match['similarity_score']:.3f}")
else:
    print(f"Search failed: {results['error']}")
```

### Live Detection

```python
from utils.live_detection import LiveVideoDetector

# Initialize detector
detector = LiveVideoDetector(
    detection_interval=0.5,
    max_detections=20
)

# Start camera
if detector.start_camera(camera_index=0):
    # Start detection
    detector.start_detection(
        query="person",
        similarity_threshold=0.3,
        save_detections=True
    )
    
    # Run for 30 seconds
    import time
    time.sleep(30)
    
    # Stop detection
    detector.stop_detection()
    
    # Get statistics
    stats = detector.get_detection_stats()
    print(f"Total detections: {stats['total_detections']}")
```

### Batch Processing

```python
from utils.clip_match import VideoSearchEngine
import os

# Initialize engine
engine = VideoSearchEngine()

# Process multiple videos
video_files = ["video1.mp4", "video2.mp4", "video3.mp4"]
query = "car"

results = {}
for video_file in video_files:
    if os.path.exists(video_file):
        result = engine.search_video(
            video_path=video_file,
            query=query,
            similarity_threshold=0.2,
            create_thumbnails=True
        )
        results[video_file] = result

# Analyze results
for video_file, result in results.items():
    if result['success']:
        print(f"{video_file}: {len(result['matches'])} matches")
    else:
        print(f"{video_file}: Failed - {result['error']}")
```

### Custom Configuration

```python
from config_advanced import AdvancedConfig
from utils.clip_match import VideoSearchEngine

# Load configuration
config = AdvancedConfig("custom_config.json")

# Customize settings
config.models.clip_model_name = "openai/clip-vit-large-patch14"
config.processing.default_frame_interval = 15
config.performance.enable_gpu = True
config.performance.batch_size = 32

# Save configuration
config.save_config()

# Create engine with custom config
engine = VideoSearchEngine(
    clip_model_name=config.models.clip_model_name,
    frame_interval=config.processing.default_frame_interval,
    enable_parallel_processing=True
)
```

### Progress Tracking

```python
from utils.clip_match import VideoSearchEngine

def progress_callback(message: str, progress: float):
    print(f"[{progress*100:.1f}%] {message}")

engine = VideoSearchEngine()

results = engine.search_video(
    video_path="video.mp4",
    query="object",
    progress_callback=progress_callback
)
```

### Error Recovery

```python
from utils.clip_match import VideoSearchEngine
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)

engine = VideoSearchEngine()

try:
    results = engine.search_video(
        video_path="video.mp4",
        query="search_query"
    )
    
    if not results['success']:
        # Try with fallback settings
        print("Retrying with fallback settings...")
        results = engine.search_video(
            video_path="video.mp4",
            query="search_query",
            similarity_threshold=0.1,  # Lower threshold
            use_advanced_matching=False,  # Disable advanced features
            create_clips=False  # Skip clip generation
        )
        
except Exception as e:
    print(f"Search failed completely: {e}")
    
    # Get error history for debugging
    error_history = engine.get_error_history()
    for error in error_history:
        print(f"Previous error: {error}")
```

---

This API reference provides comprehensive documentation for developers. For additional examples and use cases, see the examples directory and user guide.
