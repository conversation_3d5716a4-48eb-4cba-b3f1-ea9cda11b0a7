"""
Comprehensive test suite for AI-Powered Video Content Search.
Tests all major components including video search, live detection, and object extraction.
"""

import pytest
import os
import sys
import tempfile
import numpy as np
import cv2
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock
import time

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.clip_match import VideoSearchEngine
from utils.frame_extraction import FrameExtractor
from utils.live_detection import LiveVideoDetector
from utils.object_extraction import ObjectExtractor
from utils.video_slicing import VideoClipper
from models.clip_model import CLIPMatcher
from config import VideoSearchConfig


class TestVideoSearchEngine:
    """Test the main video search engine."""
    
    @pytest.fixture
    def sample_video(self):
        """Create a sample video file for testing."""
        # Create a temporary video file
        temp_dir = tempfile.mkdtemp()
        video_path = os.path.join(temp_dir, "test_video.mp4")
        
        # Create a simple test video (colored frames)
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(video_path, fourcc, 10.0, (640, 480))
        
        # Create 50 frames with different colors
        for i in range(50):
            # Create colored frame
            if i < 20:
                frame = np.full((480, 640, 3), (255, 0, 0), dtype=np.uint8)  # Red
            elif i < 40:
                frame = np.full((480, 640, 3), (0, 255, 0), dtype=np.uint8)  # Green
            else:
                frame = np.full((480, 640, 3), (0, 0, 255), dtype=np.uint8)  # Blue
            
            out.write(frame)
        
        out.release()
        yield video_path
        
        # Cleanup
        if os.path.exists(video_path):
            os.remove(video_path)
    
    @pytest.fixture
    def search_engine(self):
        """Create a search engine instance for testing."""
        return VideoSearchEngine(
            clip_model_name="openai/clip-vit-base-patch32",
            output_dir="test_output",
            frame_interval=10,
            use_chunked_processing=False,
            adaptive_config=False
        )
    
    def test_search_engine_initialization(self, search_engine):
        """Test search engine initialization."""
        assert search_engine is not None
        assert search_engine.clip_model_name == "openai/clip-vit-base-patch32"
        assert search_engine.output_dir == "test_output"
        assert search_engine.frame_extractor is not None
        assert search_engine.video_clipper is not None
    
    @patch('utils.clip_match.CLIPMatcher')
    def test_video_search_basic(self, mock_clip_matcher, search_engine, sample_video):
        """Test basic video search functionality."""
        # Mock CLIP matcher
        mock_matcher = Mock()
        mock_matcher.search_frames.return_value = [
            (0, np.zeros((100, 100, 3)), 1.0, 0.8),
            (10, np.zeros((100, 100, 3)), 2.0, 0.7)
        ]
        mock_clip_matcher.return_value = mock_matcher
        
        # Perform search
        results = search_engine.search_video(
            video_path=sample_video,
            query="red color",
            similarity_threshold=0.5,
            top_k=5,
            create_clips=False,
            create_thumbnails=False
        )
        
        # Verify results
        assert 'matches' in results
        assert 'processing_time' in results
        assert 'stats' in results
        assert results['success'] == True
        assert len(results['matches']) == 2
    
    def test_video_search_error_handling(self, search_engine):
        """Test error handling in video search."""
        # Test with non-existent video
        results = search_engine.search_video(
            video_path="non_existent_video.mp4",
            query="test query"
        )
        
        assert 'error' in results
        assert results['success'] == False
    
    def test_cache_management(self, search_engine, sample_video):
        """Test cache management functionality."""
        # Get initial cache info
        initial_cache = search_engine.get_cache_info()
        assert initial_cache['cached_videos'] == []
        
        # Mock frame extraction to avoid loading actual models
        with patch.object(search_engine, '_get_frames') as mock_get_frames:
            mock_get_frames.return_value = [
                (0, np.zeros((100, 100, 3)), 0.0),
                (1, np.zeros((100, 100, 3)), 0.1)
            ]
            
            with patch.object(search_engine, '_ensure_clip_matcher'):
                with patch.object(search_engine, 'clip_matcher') as mock_clip:
                    mock_clip.search_frames.return_value = []
                    
                    # Perform search to populate cache
                    search_engine.search_video(sample_video, "test")
        
        # Check cache after search
        cache_info = search_engine.get_cache_info()
        assert len(cache_info['cached_videos']) > 0
        
        # Test cache clearing
        search_engine.clear_cache()
        cleared_cache = search_engine.get_cache_info()
        assert cleared_cache['cached_videos'] == []


class TestFrameExtractor:
    """Test frame extraction functionality."""
    
    @pytest.fixture
    def frame_extractor(self):
        """Create a frame extractor for testing."""
        return FrameExtractor(every_n_frames=5, max_frames=20)
    
    @pytest.fixture
    def sample_video(self):
        """Create a sample video for testing."""
        temp_dir = tempfile.mkdtemp()
        video_path = os.path.join(temp_dir, "test_video.mp4")
        
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(video_path, fourcc, 10.0, (320, 240))
        
        for i in range(30):
            frame = np.random.randint(0, 255, (240, 320, 3), dtype=np.uint8)
            out.write(frame)
        
        out.release()
        yield video_path
        
        if os.path.exists(video_path):
            os.remove(video_path)
    
    def test_frame_extraction(self, frame_extractor, sample_video):
        """Test basic frame extraction."""
        frames = frame_extractor.extract_frames(sample_video)
        
        assert len(frames) > 0
        assert len(frames) <= 20  # max_frames limit
        
        # Check frame format
        frame_idx, frame_array, timestamp = frames[0]
        assert isinstance(frame_idx, int)
        assert isinstance(frame_array, np.ndarray)
        assert isinstance(timestamp, float)
        assert frame_array.shape[2] == 3  # RGB
    
    def test_video_info(self, frame_extractor, sample_video):
        """Test video information extraction."""
        info = frame_extractor.get_video_info(sample_video)
        
        assert 'duration_seconds' in info
        assert 'fps' in info
        assert 'width' in info
        assert 'height' in info
        assert 'total_frames' in info
        
        assert info['width'] == 320
        assert info['height'] == 240
        assert info['total_frames'] == 30


class TestLiveVideoDetector:
    """Test live video detection functionality."""
    
    @pytest.fixture
    def live_detector(self):
        """Create a live detector for testing."""
        return LiveVideoDetector()
    
    def test_detector_initialization(self, live_detector):
        """Test detector initialization."""
        assert live_detector is not None
        assert live_detector.clip_matcher is None  # Lazy loading
        assert live_detector.is_running == False
        assert live_detector.is_paused == False
    
    @patch('cv2.VideoCapture')
    def test_camera_start(self, mock_video_capture, live_detector):
        """Test camera initialization."""
        # Mock successful camera
        mock_cap = Mock()
        mock_cap.isOpened.return_value = True
        mock_video_capture.return_value = mock_cap
        
        success = live_detector.start_camera(0)
        assert success == True
        assert live_detector.cap is not None
    
    @patch('cv2.VideoCapture')
    def test_camera_start_failure(self, mock_video_capture, live_detector):
        """Test camera initialization failure."""
        # Mock failed camera
        mock_cap = Mock()
        mock_cap.isOpened.return_value = False
        mock_video_capture.return_value = mock_cap
        
        success = live_detector.start_camera(0)
        assert success == False
    
    def test_detection_stats(self, live_detector):
        """Test detection statistics."""
        stats = live_detector.get_detection_stats()
        
        assert 'is_running' in stats
        assert 'is_paused' in stats
        assert 'query' in stats
        assert 'similarity_threshold' in stats
        assert 'detection_interval' in stats
        assert 'queue_size' in stats
        assert 'has_camera' in stats


class TestObjectExtractor:
    """Test object extraction functionality."""
    
    @pytest.fixture
    def object_extractor(self):
        """Create an object extractor for testing."""
        return ObjectExtractor()
    
    @pytest.fixture
    def sample_image(self):
        """Create a sample image for testing."""
        # Create a simple test image with colored regions
        image = np.zeros((480, 640, 3), dtype=np.uint8)
        
        # Add colored rectangles
        image[100:200, 100:200] = [255, 0, 0]  # Red square
        image[300:400, 300:400] = [0, 255, 0]  # Green square
        image[200:300, 450:550] = [0, 0, 255]  # Blue square
        
        return image
    
    def test_extractor_initialization(self, object_extractor):
        """Test object extractor initialization."""
        assert object_extractor is not None
        assert hasattr(object_extractor, 'has_yolo')
        assert hasattr(object_extractor, 'coco_classes')
    
    @patch('utils.object_extraction.ObjectExtractor._detect_objects_yolo')
    def test_object_extraction(self, mock_detect, object_extractor, sample_image):
        """Test object extraction with mocked detection."""
        # Mock YOLO detection
        mock_detect.return_value = [
            {
                'class': 'person',
                'confidence': 0.8,
                'box': np.array([100, 100, 200, 200]),
                'label': 0
            }
        ]
        
        # Extract objects
        objects = object_extractor.extract_objects_from_frame(
            sample_image, "person", confidence_threshold=0.5
        )
        
        assert len(objects) > 0
        cropped_img, confidence, obj_class = objects[0]
        assert isinstance(cropped_img, np.ndarray)
        assert isinstance(confidence, float)
        assert isinstance(obj_class, str)
    
    def test_fallback_extraction(self, object_extractor, sample_image):
        """Test fallback extraction methods."""
        # Force fallback by setting has_yolo to False
        object_extractor.has_yolo = False
        
        objects = object_extractor.extract_objects_from_frame(
            sample_image, "test", confidence_threshold=0.5
        )
        
        # Should still return some objects using fallback methods
        assert len(objects) > 0


class TestPerformanceBenchmarks:
    """Performance benchmarks and stress tests."""
    
    def test_search_performance(self):
        """Test search performance with timing."""
        # This is a placeholder for performance testing
        # In a real scenario, you'd test with actual videos and measure timing
        start_time = time.time()
        
        # Simulate some processing
        time.sleep(0.1)
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        # Assert reasonable performance (adjust thresholds as needed)
        assert processing_time < 1.0  # Should complete in under 1 second
    
    def test_memory_usage(self):
        """Test memory usage during processing."""
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # Simulate memory-intensive operation
        large_array = np.random.random((1000, 1000, 3))
        
        current_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = current_memory - initial_memory
        
        # Clean up
        del large_array
        
        # Assert reasonable memory usage (adjust as needed)
        assert memory_increase < 100  # Should not use more than 100MB


class TestIntegration:
    """Integration tests for complete workflows."""
    
    @pytest.fixture
    def temp_output_dir(self):
        """Create temporary output directory."""
        temp_dir = tempfile.mkdtemp()
        yield temp_dir
        
        # Cleanup
        import shutil
        if os.path.exists(temp_dir):
            shutil.rmtree(temp_dir)
    
    def test_end_to_end_workflow(self, temp_output_dir):
        """Test complete end-to-end workflow."""
        # This would test the complete pipeline from video input to results
        # For now, it's a placeholder that verifies the test setup
        assert os.path.exists(temp_output_dir)
        
        # Create a test file
        test_file = os.path.join(temp_output_dir, "test.txt")
        with open(test_file, "w") as f:
            f.write("test")
        
        assert os.path.exists(test_file)


if __name__ == "__main__":
    # Run tests with pytest
    pytest.main([__file__, "-v", "--tb=short"])
