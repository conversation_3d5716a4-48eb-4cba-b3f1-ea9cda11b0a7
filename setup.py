"""
Setup script for AI-powered video content search application.
"""

import os
import subprocess
import sys


def install_requirements():
    """Install required packages."""
    print("📦 Installing required packages...")
    
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ])
        print("✅ All packages installed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error installing packages: {e}")
        return False


def create_directories():
    """Create necessary directories."""
    print("📁 Creating directories...")
    
    directories = [
        "static",
        "static/output_clips",
        "temp_videos",
        "test_output"
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"   ✅ {directory}")
    
    return True


def test_installation():
    """Test if the installation works."""
    print("🧪 Testing installation...")
    
    try:
        # Test imports
        import torch
        import cv2
        import streamlit
        from transformers import CLIPModel
        
        print("   ✅ All imports successful")
        
        # Test CUDA availability
        if torch.cuda.is_available():
            print(f"   ✅ CUDA available: {torch.cuda.get_device_name()}")
        else:
            print("   ℹ️  CUDA not available, will use CPU")
        
        return True
        
    except ImportError as e:
        print(f"   ❌ Import error: {e}")
        return False


def main():
    """Main setup function."""
    print("🚀 Setting up AI-Powered Video Content Search")
    print("=" * 50)
    
    # Create directories
    if not create_directories():
        print("❌ Failed to create directories")
        return False
    
    # Install requirements
    if not install_requirements():
        print("❌ Failed to install requirements")
        return False
    
    # Test installation
    if not test_installation():
        print("❌ Installation test failed")
        return False
    
    print("\n" + "=" * 50)
    print("🎉 Setup completed successfully!")
    print("\n💡 Next steps:")
    print("   1. Run tests: python test_application.py")
    print("   2. Start web app: python main.py --web")
    print("   3. Or use CLI: python main.py --video video.mp4 --query 'red cap'")
    print("\n📖 See README.md for detailed usage instructions")
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
