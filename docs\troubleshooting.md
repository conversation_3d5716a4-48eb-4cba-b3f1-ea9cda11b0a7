# 🛠️ Troubleshooting Guide - AI-Powered Video Content Search

This comprehensive troubleshooting guide helps you diagnose and resolve common issues with the AI-Powered Video Content Search application.

## 🔍 Quick Diagnosis

### **Run Diagnostic Tools**
```bash
# Comprehensive system check
python test_installation.py

# Quick health check
python -c "from utils.clip_match import VideoSearchEngine; print('✅ System OK')"

# Check GPU availability
python -c "import torch; print('CUDA:', torch.cuda.is_available())"
```

### **Check Logs**
```bash
# Application logs
tail -f logs/app.log

# Installation logs
cat install.log

# Docker logs (if using Docker)
docker-compose logs ai-video-search
```

## 🚨 Installation Issues

### **Python Version Problems**

**Problem:** "Python version not supported"
```bash
# Check Python version
python --version
python3 --version

# Solution: Install Python 3.8+
# Windows: Download from python.org
# macOS: brew install python@3.10
# Linux: sudo apt install python3.10
```

**Problem:** "python command not found"
```bash
# Windows: Add Python to PATH
# Linux/macOS: Use python3 instead of python
alias python=python3
```

### **Package Installation Failures**

**Problem:** "pip install fails with permission errors"
```bash
# Solution 1: Use virtual environment
python -m venv venv
source venv/bin/activate  # Linux/macOS
venv\Scripts\activate     # Windows

# Solution 2: User installation
pip install --user -r requirements.txt

# Solution 3: Fix permissions (Linux/macOS)
sudo chown -R $USER ~/.local/
```

**Problem:** "CUDA packages fail to install"
```bash
# Check CUDA version
nvidia-smi

# Install CPU-only version first
pip install torch torchvision --index-url https://download.pytorch.org/whl/cpu

# Then install CUDA version if needed
pip install torch torchvision --index-url https://download.pytorch.org/whl/cu118
```

**Problem:** "FFmpeg not found"
```bash
# Windows (Chocolatey)
choco install ffmpeg

# Windows (Manual)
# Download from https://ffmpeg.org/download.html
# Add to PATH

# macOS
brew install ffmpeg

# Linux (Ubuntu/Debian)
sudo apt update && sudo apt install ffmpeg

# Linux (CentOS/RHEL)
sudo yum install ffmpeg
```

### **Virtual Environment Issues**

**Problem:** "Virtual environment activation fails"
```bash
# Recreate virtual environment
rm -rf venv
python -m venv venv

# Windows activation
venv\Scripts\activate.bat

# Linux/macOS activation
source venv/bin/activate

# Verify activation
which python
```

## 🖥️ Application Startup Issues

### **Web Interface Problems**

**Problem:** "Streamlit won't start"
```bash
# Check if port is in use
netstat -an | grep 8501
lsof -i :8501  # Linux/macOS

# Kill process using port
kill -9 $(lsof -t -i:8501)

# Start on different port
python main.py --web --port 8502
streamlit run app/interface.py --server.port 8502
```

**Problem:** "ModuleNotFoundError when starting"
```bash
# Verify installation
pip list | grep -E "(torch|streamlit|opencv|transformers)"

# Reinstall missing packages
pip install -r requirements.txt

# Check Python path
python -c "import sys; print(sys.path)"
```

**Problem:** "Web interface loads but shows errors"
```bash
# Check browser console for JavaScript errors
# Clear browser cache and cookies
# Try incognito/private browsing mode
# Check if all static files are accessible
```

### **Command Line Issues**

**Problem:** "main.py not found"
```bash
# Ensure you're in the correct directory
ls -la main.py

# Check current directory
pwd

# Navigate to project directory
cd /path/to/ai-video-search
```

**Problem:** "Import errors in main.py"
```bash
# Check if all modules are in Python path
export PYTHONPATH="${PYTHONPATH}:$(pwd)"

# Or run with module flag
python -m main --web
```

## 🎬 Video Processing Issues

### **Video Loading Problems**

**Problem:** "Video file not supported"
```bash
# Check video format
ffprobe video.mp4

# Convert to supported format
ffmpeg -i input.mov -c:v libx264 -c:a aac output.mp4

# Supported formats: MP4, AVI, MOV, MKV, WMV, FLV, WebM
```

**Problem:** "Video file corrupted or unreadable"
```bash
# Test with FFmpeg
ffmpeg -v error -i video.mp4 -f null -

# Repair video file
ffmpeg -i broken_video.mp4 -c copy repaired_video.mp4

# Check file permissions
ls -la video.mp4
chmod 644 video.mp4
```

**Problem:** "Large video causes memory errors"
```bash
# Enable chunked processing
# Edit config.json:
{
  "processing": {
    "use_chunked_processing": true,
    "chunk_size": 100
  }
}

# Reduce frame interval
python main.py --video large_video.mp4 --query "object" --frame-interval 60

# Use lower resolution
python main.py --video large_video.mp4 --query "object" --resolution 512x384
```

### **Search Result Issues**

**Problem:** "No results found"
```bash
# Lower similarity threshold
python main.py --video video.mp4 --query "object" --threshold 0.1

# Try different query variations
# "car" → "vehicle", "automobile", "sedan"
# "person" → "human", "man", "woman", "people"

# Check if video contains searched content
# Use very general queries first: "object", "scene", "movement"
```

**Problem:** "Too many false positives"
```bash
# Increase similarity threshold
python main.py --video video.mp4 --query "object" --threshold 0.3

# Use more specific queries
# "red car" instead of "car"
# "person walking" instead of "person"

# Enable advanced matching
python main.py --video video.mp4 --query "object" --advanced-matching
```

**Problem:** "Processing is very slow"
```bash
# Check GPU usage
nvidia-smi  # Should show Python process using GPU

# Enable GPU if not active
export CUDA_VISIBLE_DEVICES=0

# Increase frame interval (process fewer frames)
python main.py --video video.mp4 --query "object" --frame-interval 60

# Use smaller CLIP model
# Edit config.json:
{
  "models": {
    "clip_model_name": "openai/clip-vit-base-patch32"
  }
}
```

## 🎮 GPU and Performance Issues

### **CUDA Problems**

**Problem:** "CUDA out of memory"
```bash
# Check GPU memory usage
nvidia-smi

# Reduce batch size
# Edit config.json:
{
  "performance": {
    "batch_size": 8
  }
}

# Clear GPU cache
python -c "import torch; torch.cuda.empty_cache()"

# Use CPU instead
export CUDA_VISIBLE_DEVICES=""
```

**Problem:** "CUDA not available despite having NVIDIA GPU"
```bash
# Check NVIDIA driver
nvidia-smi

# Check CUDA installation
nvcc --version

# Reinstall PyTorch with CUDA
pip uninstall torch torchvision
pip install torch torchvision --index-url https://download.pytorch.org/whl/cu118

# Check PyTorch CUDA
python -c "import torch; print(torch.cuda.is_available(), torch.version.cuda)"
```

### **Memory Issues**

**Problem:** "System runs out of memory"
```bash
# Monitor memory usage
htop  # Linux/macOS
taskmgr  # Windows

# Enable memory monitoring
# Edit config.json:
{
  "memory": {
    "enable_memory_monitoring": true,
    "auto_cleanup_threshold": 0.8
  }
}

# Reduce cache size
{
  "memory": {
    "max_cache_size_mb": 1024
  }
}

# Process videos in smaller chunks
{
  "processing": {
    "chunk_size": 50
  }
}
```

## 📹 Live Detection Issues

### **Camera Problems**

**Problem:** "Camera not detected"
```bash
# List available cameras (Linux)
ls /dev/video*

# Test camera access
python -c "import cv2; cap = cv2.VideoCapture(0); print('Camera OK:', cap.isOpened())"

# Try different camera indices
python main.py --live --query "person" --camera 1

# Check camera permissions (macOS)
# System Preferences → Security & Privacy → Camera
```

**Problem:** "RTSP stream connection fails"
```bash
# Test RTSP URL with FFmpeg
ffplay rtsp://*************:554/stream

# Check network connectivity
ping *************

# Try different RTSP URLs
rtsp://username:password@ip:port/stream
rtsp://ip:port/live/ch1
```

**Problem:** "Live detection is laggy"
```bash
# Increase detection interval
python main.py --live --query "person" --detection-interval 1.0

# Reduce camera resolution
# Configure in camera settings or RTSP parameters

# Use faster CLIP model
# Edit config.json for smaller model
```

## 🐳 Docker Issues

### **Docker Installation Problems**

**Problem:** "Docker not found"
```bash
# Install Docker
curl -fsSL https://get.docker.com | sh

# Start Docker service (Linux)
sudo systemctl start docker
sudo systemctl enable docker

# Add user to docker group
sudo usermod -aG docker $USER
# Logout and login again
```

**Problem:** "Docker Compose not found"
```bash
# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Or use Docker Compose plugin
docker compose version
```

### **Container Issues**

**Problem:** "Container fails to start"
```bash
# Check container logs
docker-compose logs ai-video-search

# Check container status
docker-compose ps

# Rebuild container
docker-compose down
docker-compose build --no-cache
docker-compose up -d
```

**Problem:** "Port already in use"
```bash
# Check what's using the port
sudo netstat -tulpn | grep 8501

# Change port in .env file
echo "WEB_PORT=8502" >> .env

# Or stop conflicting service
docker stop $(docker ps -q --filter "publish=8501")
```

## 🔧 Configuration Issues

### **Config File Problems**

**Problem:** "Configuration file not found"
```bash
# Create default config
python -c "from config_advanced import AdvancedConfig; AdvancedConfig().save_config()"

# Check config file location
ls -la config.json

# Verify config syntax
python -c "import json; json.load(open('config.json'))"
```

**Problem:** "Invalid configuration values"
```bash
# Validate configuration
python -c "from config_advanced import AdvancedConfig; AdvancedConfig().validate_config()"

# Reset to defaults
rm config.json
python install.py
```

## 📊 Performance Optimization

### **Speed Improvements**

```bash
# Enable GPU acceleration
# Ensure CUDA is properly installed and configured

# Optimize frame sampling
# Increase frame_interval for faster processing
# Decrease for more thorough analysis

# Use appropriate CLIP model
# Smaller models: clip-vit-base-patch32 (faster)
# Larger models: clip-vit-large-patch14 (more accurate)

# Enable parallel processing
{
  "performance": {
    "enable_parallel_processing": true,
    "max_workers": 4
  }
}
```

### **Memory Optimization**

```bash
# Enable chunked processing
{
  "processing": {
    "use_chunked_processing": true,
    "chunk_size": 100
  }
}

# Optimize cache settings
{
  "memory": {
    "max_cache_size_mb": 2048,
    "enable_memory_monitoring": true
  }
}

# Use lower resolution for processing
{
  "processing": {
    "target_resolution": [512, 384]
  }
}
```

## 🆘 Getting Additional Help

### **Diagnostic Information to Collect**

When reporting issues, please include:

```bash
# System information
python test_installation.py > diagnostic_report.txt

# Version information
python --version
pip list > installed_packages.txt

# Error logs
cat logs/app.log > error_logs.txt

# Configuration
cat config.json > current_config.txt
```

### **Support Channels**

1. **📖 Documentation:** Check [Complete User Guide](COMPLETE_USER_GUIDE.md)
2. **❓ FAQ:** Review [Frequently Asked Questions](faq.md)
3. **🐛 GitHub Issues:** Report bugs and request features
4. **💬 Community:** Join discussions for community support
5. **📧 Professional Support:** Contact for enterprise support

### **Before Reporting Issues**

1. **Update to latest version**
2. **Check known issues** in documentation
3. **Try basic troubleshooting steps**
4. **Collect diagnostic information**
5. **Search existing issues** on GitHub

---

**Remember:** Most issues can be resolved by following the steps in this guide. If you're still experiencing problems, don't hesitate to reach out for help with detailed diagnostic information!
