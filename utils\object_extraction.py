"""
Object extraction and cropping utilities to isolate specific objects from video frames.
"""

import cv2
import numpy as np
from typing import List, Tuple, Optional, Dict, Any
from PIL import Image, ImageDraw, ImageFont
import torch


class ObjectExtractor:
    """Extract and crop specific objects from images using simple computer vision techniques."""

    def __init__(self):
        """Initialize object extractor with basic CV methods."""
        print("Initializing object extractor with computer vision methods...")

        # Load pre-trained object detection if available
        try:
            # Try to load YOLOv5 (lightweight)
            self.yolo_model = torch.hub.load('ultralytics/yolov5', 'yolov5s', pretrained=True)
            self.yolo_model.eval()
            self.has_yolo = True
            print("✅ YOLOv5 model loaded successfully")
        except Exception as e:
            print(f"⚠️ Could not load YOLOv5: {e}")
            print("Using basic computer vision methods")
            self.has_yolo = False
        
        # COCO class names for DETR
        self.coco_classes = [
            'N/A', 'person', 'bicycle', 'car', 'motorcycle', 'airplane', 'bus',
            'train', 'truck', 'boat', 'traffic light', 'fire hydrant', 'N/A',
            'stop sign', 'parking meter', 'bench', 'bird', 'cat', 'dog', 'horse',
            'sheep', 'cow', 'elephant', 'bear', 'zebra', 'giraffe', 'N/A', 'backpack',
            'umbrella', 'N/A', 'N/A', 'handbag', 'tie', 'suitcase', 'frisbee', 'skis',
            'snowboard', 'sports ball', 'kite', 'baseball bat', 'baseball glove',
            'skateboard', 'surfboard', 'tennis racket', 'bottle', 'N/A', 'wine glass',
            'cup', 'fork', 'knife', 'spoon', 'bowl', 'banana', 'apple', 'sandwich',
            'orange', 'broccoli', 'carrot', 'hot dog', 'pizza', 'donut', 'cake',
            'chair', 'couch', 'potted plant', 'bed', 'N/A', 'dining table', 'N/A',
            'N/A', 'toilet', 'N/A', 'tv', 'laptop', 'mouse', 'remote', 'keyboard',
            'cell phone', 'microwave', 'oven', 'toaster', 'sink', 'refrigerator',
            'N/A', 'book', 'clock', 'vase', 'scissors', 'teddy bear', 'hair drier',
            'toothbrush'
        ]
    
    def extract_objects_from_frame(self, image: np.ndarray, query: str, 
                                 confidence_threshold: float = 0.7,
                                 max_objects: int = 5) -> List[Tuple[np.ndarray, float, str]]:
        """
        Extract objects matching the query from a video frame.
        
        Args:
            image: Input image array (RGB)
            query: Search query (e.g., "car", "person", "dog")
            confidence_threshold: Minimum confidence for object detection
            max_objects: Maximum number of objects to extract
            
        Returns:
            List of (cropped_object_image, confidence, object_class) tuples
        """
        if self.model is None:
            # Fallback to center cropping if no object detection model
            return self._fallback_center_crop(image, query)
        
        try:
            # Detect objects in the image
            detections = self._detect_objects(image, confidence_threshold)
            
            # Filter detections based on query
            relevant_objects = self._filter_detections_by_query(detections, query)
            
            # Extract and crop objects
            extracted_objects = []
            for detection in relevant_objects[:max_objects]:
                cropped_obj = self._crop_object(image, detection)
                if cropped_obj is not None:
                    extracted_objects.append((
                        cropped_obj,
                        detection['confidence'],
                        detection['class']
                    ))
            
            return extracted_objects
            
        except Exception as e:
            print(f"Error in object extraction: {e}")
            return self._fallback_center_crop(image, query)
    
    def _detect_objects(self, image: np.ndarray, confidence_threshold: float) -> List[Dict[str, Any]]:
        """Detect objects in the image using DETR."""
        # Convert to PIL Image
        pil_image = Image.fromarray(image)
        
        # Process image
        inputs = self.processor(images=pil_image, return_tensors="pt")
        inputs = {k: v.to(self.device) for k, v in inputs.items()}
        
        # Run inference
        with torch.no_grad():
            outputs = self.model(**inputs)
        
        # Process outputs
        target_sizes = torch.tensor([pil_image.size[::-1]]).to(self.device)
        results = self.processor.post_process_object_detection(
            outputs, target_sizes=target_sizes, threshold=confidence_threshold
        )[0]
        
        # Convert to list of detections
        detections = []
        for score, label, box in zip(results["scores"], results["labels"], results["boxes"]):
            class_name = self.coco_classes[label.item()]
            if class_name != 'N/A':
                detections.append({
                    'class': class_name,
                    'confidence': score.item(),
                    'box': box.cpu().numpy(),  # [x_min, y_min, x_max, y_max]
                    'label': label.item()
                })
        
        return detections
    
    def _filter_detections_by_query(self, detections: List[Dict[str, Any]], query: str) -> List[Dict[str, Any]]:
        """Filter detections based on the search query."""
        query_lower = query.lower()
        relevant_detections = []
        
        # Direct class name matching
        for detection in detections:
            class_name = detection['class'].lower()
            
            # Check for exact matches or partial matches
            if (class_name in query_lower or 
                query_lower in class_name or
                self._is_related_class(query_lower, class_name)):
                relevant_detections.append(detection)
        
        # Sort by confidence
        relevant_detections.sort(key=lambda x: x['confidence'], reverse=True)
        return relevant_detections
    
    def _is_related_class(self, query: str, class_name: str) -> bool:
        """Check if class name is related to the query."""
        # Define related terms
        relations = {
            'car': ['car', 'truck', 'bus', 'vehicle'],
            'vehicle': ['car', 'truck', 'bus', 'motorcycle', 'bicycle'],
            'person': ['person'],
            'people': ['person'],
            'human': ['person'],
            'animal': ['dog', 'cat', 'horse', 'cow', 'sheep', 'bird', 'elephant', 'bear', 'zebra', 'giraffe'],
            'pet': ['dog', 'cat'],
            'dog': ['dog'],
            'cat': ['cat'],
            'bird': ['bird'],
            'furniture': ['chair', 'couch', 'bed', 'dining table'],
            'electronics': ['tv', 'laptop', 'cell phone', 'remote'],
            'food': ['banana', 'apple', 'sandwich', 'orange', 'pizza', 'cake']
        }
        
        # Check if query matches any relation
        for key, values in relations.items():
            if key in query:
                return class_name in values
        
        return False
    
    def _crop_object(self, image: np.ndarray, detection: Dict[str, Any], 
                    padding: int = 20) -> Optional[np.ndarray]:
        """Crop object from image based on detection box."""
        try:
            box = detection['box']
            x_min, y_min, x_max, y_max = map(int, box)
            
            # Add padding
            h, w = image.shape[:2]
            x_min = max(0, x_min - padding)
            y_min = max(0, y_min - padding)
            x_max = min(w, x_max + padding)
            y_max = min(h, y_max + padding)
            
            # Crop the object
            cropped = image[y_min:y_max, x_min:x_max]
            
            # Ensure minimum size
            if cropped.shape[0] < 50 or cropped.shape[1] < 50:
                return None
            
            return cropped
            
        except Exception as e:
            print(f"Error cropping object: {e}")
            return None
    
    def _fallback_center_crop(self, image: np.ndarray, query: str) -> List[Tuple[np.ndarray, float, str]]:
        """Fallback method using center cropping and saliency detection."""
        try:
            # Use saliency detection to find interesting regions
            saliency_regions = self._detect_saliency_regions(image)
            
            extracted_objects = []
            for i, region in enumerate(saliency_regions[:3]):  # Top 3 regions
                cropped = self._crop_region(image, region)
                if cropped is not None:
                    extracted_objects.append((cropped, 0.5, f"region_{i+1}"))
            
            return extracted_objects
            
        except Exception:
            # Ultimate fallback: center crop
            h, w = image.shape[:2]
            center_x, center_y = w // 2, h // 2
            crop_size = min(h, w) // 2
            
            x_min = max(0, center_x - crop_size // 2)
            y_min = max(0, center_y - crop_size // 2)
            x_max = min(w, center_x + crop_size // 2)
            y_max = min(h, center_y + crop_size // 2)
            
            cropped = image[y_min:y_max, x_min:x_max]
            return [(cropped, 0.3, "center_region")]
    
    def _detect_saliency_regions(self, image: np.ndarray) -> List[Tuple[int, int, int, int]]:
        """Detect salient regions in the image using simple methods."""
        # Convert to grayscale
        gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
        
        # Use edge detection to find interesting regions
        edges = cv2.Canny(gray, 50, 150)
        
        # Find contours
        contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        # Get bounding boxes of largest contours
        regions = []
        for contour in sorted(contours, key=cv2.contourArea, reverse=True)[:5]:
            x, y, w, h = cv2.boundingRect(contour)
            if w > 50 and h > 50:  # Minimum size
                regions.append((x, y, x + w, y + h))
        
        return regions
    
    def _crop_region(self, image: np.ndarray, region: Tuple[int, int, int, int], 
                    padding: int = 10) -> Optional[np.ndarray]:
        """Crop a specific region from the image."""
        try:
            x_min, y_min, x_max, y_max = region
            h, w = image.shape[:2]
            
            # Add padding
            x_min = max(0, x_min - padding)
            y_min = max(0, y_min - padding)
            x_max = min(w, x_max + padding)
            y_max = min(h, y_max + padding)
            
            cropped = image[y_min:y_max, x_min:x_max]
            
            # Ensure minimum size
            if cropped.shape[0] < 50 or cropped.shape[1] < 50:
                return None
            
            return cropped
            
        except Exception:
            return None
    
    def create_object_collage(self, objects: List[Tuple[np.ndarray, float, str]], 
                            query: str, max_size: Tuple[int, int] = (800, 600)) -> np.ndarray:
        """Create a collage of extracted objects."""
        if not objects:
            # Return a placeholder image
            placeholder = np.ones((200, 400, 3), dtype=np.uint8) * 128
            return placeholder
        
        # Calculate grid layout
        num_objects = len(objects)
        cols = min(3, num_objects)
        rows = (num_objects + cols - 1) // cols
        
        # Calculate cell size
        cell_width = max_size[0] // cols
        cell_height = max_size[1] // rows
        
        # Create collage canvas
        collage = np.ones((rows * cell_height, cols * cell_width, 3), dtype=np.uint8) * 255
        
        for i, (obj_img, confidence, obj_class) in enumerate(objects):
            row = i // cols
            col = i % cols
            
            # Resize object to fit cell
            obj_resized = cv2.resize(obj_img, (cell_width - 20, cell_height - 40))
            
            # Calculate position
            y_start = row * cell_height + 10
            x_start = col * cell_width + 10
            y_end = y_start + obj_resized.shape[0]
            x_end = x_start + obj_resized.shape[1]
            
            # Place object in collage
            collage[y_start:y_end, x_start:x_end] = obj_resized
            
            # Add label (convert to PIL for text)
            pil_collage = Image.fromarray(collage)
            draw = ImageDraw.Draw(pil_collage)
            
            # Add text label
            label = f"{obj_class} ({confidence:.2f})"
            text_y = y_end + 5
            draw.text((x_start, text_y), label, fill=(0, 0, 0))
            
            collage = np.array(pil_collage)
        
        return collage


def create_object_extractor(device: Optional[str] = None) -> ObjectExtractor:
    """Factory function to create an object extractor."""
    return ObjectExtractor(device=device)
