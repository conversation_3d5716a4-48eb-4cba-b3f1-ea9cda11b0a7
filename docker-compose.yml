version: '3.8'

services:
  # Main AI Video Search Application
  ai-video-search:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        INSTALL_GPU: ${INSTALL_GPU:-false}
        BUILD_ENV: ${BUILD_ENV:-production}
    container_name: ai-video-search-app
    ports:
      - "${WEB_PORT:-8501}:8501"
    volumes:
      # Persistent data storage
      - ./data:/app/data
      - ./static/output_clips:/app/static/output_clips
      - ./temp_videos:/app/temp_videos
      - ./logs:/app/logs
      # Configuration
      - ./config.json:/app/config.json:ro
      # Video input directory (mount your videos here)
      - ${VIDEO_INPUT_DIR:-./videos}:/app/input_videos:ro
    environment:
      - STREAMLIT_SERVER_PORT=8501
      - STREAMLIT_SERVER_ADDRESS=0.0.0.0
      - STREAMLIT_SERVER_HEADLESS=true
      - STREAMLIT_BROWSER_GATHER_USAGE_STATS=false
      - PYTHONUNBUFFERED=1
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8501/_stcore/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    networks:
      - ai-video-network
    # GPU support (uncomment if using NVIDIA GPU)
    # deploy:
    #   resources:
    #     reservations:
    #       devices:
    #         - driver: nvidia
    #           count: 1
    #           capabilities: [gpu]

  # Redis for caching (optional)
  redis:
    image: redis:7-alpine
    container_name: ai-video-search-redis
    ports:
      - "${REDIS_PORT:-6379}:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes --maxmemory 512mb --maxmemory-policy allkeys-lru
    restart: unless-stopped
    networks:
      - ai-video-network
    profiles:
      - with-redis

  # PostgreSQL for metadata storage (optional)
  postgres:
    image: postgres:15-alpine
    container_name: ai-video-search-db
    ports:
      - "${POSTGRES_PORT:-5432}:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./docker/postgres/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    environment:
      - POSTGRES_DB=${POSTGRES_DB:-ai_video_search}
      - POSTGRES_USER=${POSTGRES_USER:-postgres}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-password}
    restart: unless-stopped
    networks:
      - ai-video-network
    profiles:
      - with-database

  # Nginx reverse proxy (optional)
  nginx:
    image: nginx:alpine
    container_name: ai-video-search-nginx
    ports:
      - "${NGINX_PORT:-80}:80"
      - "${NGINX_SSL_PORT:-443}:443"
    volumes:
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./docker/nginx/ssl:/etc/nginx/ssl:ro
    depends_on:
      - ai-video-search
    restart: unless-stopped
    networks:
      - ai-video-network
    profiles:
      - with-nginx

  # Monitoring with Prometheus (optional)
  prometheus:
    image: prom/prometheus:latest
    container_name: ai-video-search-prometheus
    ports:
      - "${PROMETHEUS_PORT:-9090}:9090"
    volumes:
      - ./docker/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    restart: unless-stopped
    networks:
      - ai-video-network
    profiles:
      - with-monitoring

  # Grafana for visualization (optional)
  grafana:
    image: grafana/grafana:latest
    container_name: ai-video-search-grafana
    ports:
      - "${GRAFANA_PORT:-3000}:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./docker/grafana/provisioning:/etc/grafana/provisioning:ro
      - ./docker/grafana/dashboards:/var/lib/grafana/dashboards:ro
    environment:
      - GF_SECURITY_ADMIN_USER=${GRAFANA_USER:-admin}
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin}
      - GF_USERS_ALLOW_SIGN_UP=false
    depends_on:
      - prometheus
    restart: unless-stopped
    networks:
      - ai-video-network
    profiles:
      - with-monitoring

networks:
  ai-video-network:
    driver: bridge

volumes:
  redis_data:
    driver: local
  postgres_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
