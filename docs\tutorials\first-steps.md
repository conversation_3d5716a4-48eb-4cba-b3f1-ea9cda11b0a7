# 👶 First Steps Tutorial - Your First Video Search

Welcome to AI-Powered Video Content Search! This tutorial will guide you through your very first video search, step by step. By the end, you'll understand the basics and be ready to explore more advanced features.

## 🎯 What You'll Learn

- How to upload and search your first video
- Understanding search results and similarity scores
- Basic query optimization techniques
- How to download and use results

## 📋 Prerequisites

- ✅ AI-Powered Video Content Search installed and running
- ✅ Web interface accessible at http://localhost:8501
- 📹 A sample video file (any format: MP4, AVI, MOV, etc.)

**Don't have a test video?** Download a sample from:
- [Sample Videos](https://sample-videos.com/)
- [Pixabay Videos](https://pixabay.com/videos/)
- Or use any video from your phone/computer

## 🚀 Step 1: Start the Application

### Option A: Web Interface (Recommended for beginners)
```bash
# Start the web interface
python main.py --web

# Or use launcher script
./launch_web.sh      # Linux/macOS
launch_web.bat       # Windows
```

### Option B: Check if Already Running
Open your browser and go to: **http://localhost:8501**

If you see the AI-Powered Video Content Search interface, you're ready to go!

## 📁 Step 2: Upload Your First Video

1. **Navigate to Video File Search Tab**
   - You should see tabs at the top: "Video File Search", "Live Video Detection", etc.
   - Click on "📁 Video File Search" (should be selected by default)

2. **Upload Your Video**
   - Look for the file uploader section
   - Click "Browse files" or drag and drop your video file
   - **Supported formats:** MP4, AVI, MOV, MKV, WMV, FLV, WebM
   - **File size:** Start with smaller files (< 500MB) for your first try

3. **Wait for Upload**
   - You'll see a progress bar during upload
   - Once complete, you'll see the filename displayed

**💡 Tip:** For your first search, use a video that's 1-5 minutes long with clear, distinct objects or people.

## 🔍 Step 3: Enter Your Search Query

1. **Choose What to Search For**
   Think about what's actually in your video. Good first queries:
   - **"person"** - if there are people in the video
   - **"car"** - if there are vehicles
   - **"dog"** or **"cat"** - if there are pets
   - **"building"** - for architectural elements
   - **"tree"** - for outdoor scenes

2. **Enter Your Query**
   - Find the "Search Query" text box
   - Type your chosen search term (start simple!)
   - Example: `person`

3. **Configure Basic Settings**
   - **Similarity Threshold:** Leave at default (0.2) for now
   - **Max Results:** Leave at default (20)
   - **Create Thumbnails:** ✅ Keep checked (you want to see results!)
   - **Create Video Clips:** ⬜ Uncheck for faster processing on first try

**💡 Tip:** Start with simple, single-word queries. You can get more specific later!

## ⚙️ Step 4: Start Your First Search

1. **Click the Search Button**
   - Look for a button labeled "🔍 Search Video" or similar
   - Click it to start processing

2. **Watch the Progress**
   - You'll see progress indicators showing:
     - Frame extraction progress
     - Search processing progress
     - Results compilation

3. **Processing Time**
   - **Small videos (< 100MB):** 30 seconds - 2 minutes
   - **Medium videos (100MB - 1GB):** 2-10 minutes
   - **Large videos (> 1GB):** 10+ minutes

**💡 Tip:** Don't close the browser tab while processing! You can do other things, but keep the tab open.

## 📊 Step 5: Understanding Your Results

### **Results Overview**
After processing, you'll see:
- **Total matches found:** Number of matching frames
- **Processing time:** How long the search took
- **Video information:** Duration, resolution, frame count

### **Individual Results**
Each result shows:
- **Thumbnail image:** What was detected
- **Timestamp:** When in the video (e.g., "00:01:23")
- **Similarity score:** How confident the AI is (0.0 - 1.0)
- **Frame number:** Technical reference

### **Interpreting Similarity Scores**
- **0.4 - 1.0:** 🎯 Excellent match (very confident)
- **0.3 - 0.4:** ✅ Good match (confident)
- **0.2 - 0.3:** 👍 Decent match (moderately confident)
- **0.1 - 0.2:** 🤔 Possible match (low confidence)
- **< 0.1:** ❌ Poor match (likely false positive)

### **What Good Results Look Like**
- **Clear thumbnails** showing the searched object
- **Reasonable timestamps** spread throughout the video
- **Similarity scores** mostly above 0.2
- **Recognizable content** that matches your query

## 🎉 Step 6: Exploring Your Results

### **View Full-Size Images**
- Click on any thumbnail to see it full-size
- Use this to verify the AI found what you were looking for
- Look for clear, well-framed instances of your search term

### **Navigate Through Results**
- Scroll through all results
- Notice how similarity scores vary
- Identify the best matches (highest scores + clearest images)

### **Check Timestamps**
- Note when in the video each match occurs
- This helps you understand the distribution of your searched content
- You can use these timestamps to navigate to specific moments

## 📥 Step 7: Download and Use Results

### **Download Individual Images**
- Right-click on any thumbnail
- Select "Save image as..." or "Download image"
- Choose where to save on your computer

### **Bulk Download (if available)**
- Look for "Download All Results" or similar button
- This downloads all matching images as a ZIP file

### **Copy Timestamps**
- Note down interesting timestamps for later reference
- Format: MM:SS or HH:MM:SS
- Use these to jump to specific moments in your original video

## 🔄 Step 8: Try Different Queries

Now that you've completed your first search, experiment with variations:

### **More Specific Queries**
If you searched for "person", try:
- **"man"** or **"woman"**
- **"person walking"**
- **"person wearing glasses"**
- **"child"** or **"adult"**

### **Different Objects**
Try searching for other things you saw in the video:
- **"red car"** (if you saw vehicles)
- **"outdoor scene"** (for landscape shots)
- **"indoor room"** (for interior shots)
- **"close-up face"** (for portrait shots)

### **Adjust Similarity Threshold**
- **Lower threshold (0.1):** More results, some false positives
- **Higher threshold (0.3):** Fewer results, higher accuracy

## 🎓 What You've Learned

Congratulations! You've successfully:
- ✅ Uploaded and processed your first video
- ✅ Performed a search with natural language
- ✅ Understood similarity scores and results
- ✅ Downloaded and explored search results
- ✅ Experimented with different queries

## 🚀 Next Steps

### **Immediate Next Steps**
1. **Try more videos** with different content types
2. **Experiment with longer, more descriptive queries**
3. **Test the similarity threshold settings**
4. **Enable video clip creation** for your next search

### **Explore More Features**
- [**Live Detection Tutorial**](live-detection-setup.md) - Real-time camera search
- [**Advanced Search Techniques**](advanced-search.md) - Complex queries and filters
- [**Batch Processing**](batch-processing.md) - Multiple videos at once
- [**Command Line Usage**](cli-examples.md) - Automation and scripting

### **Learn More**
- [**Complete User Guide**](../COMPLETE_USER_GUIDE.md) - All features explained
- [**Web Interface Guide**](../web-interface-guide.md) - Detailed UI walkthrough
- [**Performance Optimization**](../OPTIMIZATION_SUMMARY.md) - Speed and accuracy tips

## 🆘 Troubleshooting Your First Search

### **No Results Found**
- **Lower the similarity threshold** to 0.1 or 0.15
- **Try broader queries** like "object" or "scene"
- **Check if your query matches video content**
- **Verify video uploaded correctly**

### **Too Many False Positives**
- **Raise the similarity threshold** to 0.3 or higher
- **Use more specific queries** with descriptive adjectives
- **Check video quality** - blurry videos produce poor results

### **Processing Takes Too Long**
- **Use smaller videos** for learning (< 500MB)
- **Increase frame interval** in advanced settings
- **Disable video clip creation** for faster processing

### **Web Interface Issues**
- **Refresh the browser page**
- **Check if application is still running**
- **Try a different browser** (Chrome, Firefox, Safari)
- **Clear browser cache** if needed

## 💡 Pro Tips for Beginners

1. **Start Simple:** Use single-word queries before trying complex descriptions
2. **Good Video Quality:** Clear, well-lit videos produce better results
3. **Realistic Expectations:** AI is powerful but not perfect - some misses are normal
4. **Experiment:** Try different queries for the same content to see variations
5. **Save Good Queries:** Note down query/threshold combinations that work well
6. **Be Patient:** Processing takes time, especially for longer videos

## 🎉 Congratulations!

You've completed your first video search! You now understand the basics of how AI-powered video content search works. The more you experiment with different videos and queries, the better you'll become at crafting effective searches.

**Ready for more?** Check out our [Advanced Search Techniques](advanced-search.md) tutorial to learn about complex queries, filters, and optimization techniques!

---

**Need Help?** 
- 📖 [Complete User Guide](../COMPLETE_USER_GUIDE.md)
- 🛠️ [Troubleshooting Guide](../troubleshooting.md)
- ❓ [FAQ](../faq.md)
