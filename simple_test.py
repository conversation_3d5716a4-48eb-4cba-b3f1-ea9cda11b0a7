"""
Simple test script to verify the installation works.
This script tests basic functionality without complex imports.
"""

import os
import sys

def test_basic_imports():
    """Test if all required packages can be imported."""
    print("🧪 Testing basic imports...")
    
    try:
        import torch
        print("   ✅ PyTorch imported successfully")
        
        import cv2
        print("   ✅ OpenCV imported successfully")
        
        import streamlit
        print("   ✅ Streamlit imported successfully")
        
        from transformers import CLIPModel, CLIPProcessor
        print("   ✅ Transformers (CLIP) imported successfully")
        
        import moviepy
        print("   ✅ MoviePy imported successfully")
        
        import numpy as np
        print("   ✅ NumPy imported successfully")
        
        from PIL import Image
        print("   ✅ PIL imported successfully")
        
        return True
        
    except ImportError as e:
        print(f"   ❌ Import failed: {e}")
        return False


def test_torch_functionality():
    """Test basic PyTorch functionality."""
    print("\n🔥 Testing PyTorch functionality...")
    
    try:
        import torch
        
        # Test basic tensor operations
        x = torch.randn(3, 3)
        y = torch.randn(3, 3)
        z = torch.matmul(x, y)
        print(f"   ✅ Tensor operations work: {z.shape}")
        
        # Test CUDA availability
        if torch.cuda.is_available():
            print(f"   ✅ CUDA available: {torch.cuda.get_device_name()}")
            device = "cuda"
        else:
            print("   ℹ️  CUDA not available, using CPU")
            device = "cpu"
        
        # Test moving tensors to device
        x_device = x.to(device)
        print(f"   ✅ Tensor moved to {device}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ PyTorch test failed: {e}")
        return False


def test_clip_model_loading():
    """Test loading the CLIP model."""
    print("\n🤖 Testing CLIP model loading...")
    
    try:
        from transformers import CLIPModel, CLIPProcessor
        import torch
        
        print("   📥 Loading CLIP model (this may take a moment)...")
        model = CLIPModel.from_pretrained("openai/clip-vit-base-patch32")
        processor = CLIPProcessor.from_pretrained("openai/clip-vit-base-patch32")
        
        print("   ✅ CLIP model loaded successfully")
        
        # Test basic encoding
        text_inputs = processor(text=["a red car"], return_tensors="pt", padding=True)
        text_features = model.get_text_features(**text_inputs)
        print(f"   ✅ Text encoding works: {text_features.shape}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ CLIP model test failed: {e}")
        print("   💡 This might be due to network issues or insufficient memory")
        return False


def test_opencv_functionality():
    """Test OpenCV video functionality."""
    print("\n🎥 Testing OpenCV functionality...")
    
    try:
        import cv2
        import numpy as np
        
        # Create a simple test video
        print("   📹 Creating test video...")
        
        # Video properties
        width, height = 320, 240
        fps = 30
        duration = 2  # seconds
        total_frames = fps * duration
        
        # Create video writer
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        test_video_path = "test_video.mp4"
        out = cv2.VideoWriter(test_video_path, fourcc, fps, (width, height))
        
        # Generate frames
        for i in range(total_frames):
            # Create a colored frame
            color = (i * 255 // total_frames, 100, 200)
            frame = np.full((height, width, 3), color, dtype=np.uint8)
            out.write(frame)
        
        out.release()
        print(f"   ✅ Test video created: {test_video_path}")
        
        # Test reading the video
        cap = cv2.VideoCapture(test_video_path)
        frame_count = 0
        while cap.isOpened():
            ret, frame = cap.read()
            if not ret:
                break
            frame_count += 1
        cap.release()
        
        print(f"   ✅ Video reading works: {frame_count} frames read")
        
        # Clean up
        if os.path.exists(test_video_path):
            os.remove(test_video_path)
        
        return True
        
    except Exception as e:
        print(f"   ❌ OpenCV test failed: {e}")
        return False


def test_directories():
    """Test if required directories exist."""
    print("\n📁 Testing directories...")
    
    required_dirs = [
        "utils",
        "models", 
        "app",
        "static",
        "static/output_clips"
    ]
    
    all_exist = True
    for directory in required_dirs:
        if os.path.exists(directory):
            print(f"   ✅ {directory}")
        else:
            print(f"   ❌ {directory} (missing)")
            all_exist = False
    
    return all_exist


def test_files():
    """Test if required files exist."""
    print("\n📄 Testing files...")
    
    required_files = [
        "main.py",
        "requirements.txt",
        "utils/frame_extraction.py",
        "utils/clip_match.py", 
        "utils/video_slicing.py",
        "models/clip_model.py",
        "app/interface.py"
    ]
    
    all_exist = True
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"   ✅ {file_path}")
        else:
            print(f"   ❌ {file_path} (missing)")
            all_exist = False
    
    return all_exist


def main():
    """Run all tests."""
    print("🚀 AI Video Search - Simple Installation Test")
    print("=" * 60)
    
    test_results = []
    
    # Test basic imports
    imports_ok = test_basic_imports()
    test_results.append(("Basic Imports", imports_ok))
    
    # Test PyTorch
    torch_ok = test_torch_functionality()
    test_results.append(("PyTorch", torch_ok))
    
    # Test directories and files
    dirs_ok = test_directories()
    test_results.append(("Directories", dirs_ok))
    
    files_ok = test_files()
    test_results.append(("Files", files_ok))
    
    # Test OpenCV
    opencv_ok = test_opencv_functionality()
    test_results.append(("OpenCV", opencv_ok))
    
    # Test CLIP model (only if other tests pass)
    if imports_ok and torch_ok:
        clip_ok = test_clip_model_loading()
        test_results.append(("CLIP Model", clip_ok))
    else:
        test_results.append(("CLIP Model", False))
    
    # Print results
    print("\n" + "=" * 60)
    print("TEST RESULTS")
    print("=" * 60)
    
    all_passed = True
    for test_name, passed in test_results:
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{test_name:<20} {status}")
        if not passed:
            all_passed = False
    
    print("=" * 60)
    
    if all_passed:
        print("🎉 ALL TESTS PASSED!")
        print("\n✅ Your installation is working correctly!")
        print("\n💡 Next steps:")
        print("   1. Run: python main.py --web")
        print("   2. Open browser to: http://localhost:8501")
        print("   3. Upload a video and start searching!")
    else:
        print("⚠️  Some tests failed.")
        print("\n🔧 Troubleshooting:")
        print("   1. Install missing packages: pip install -r requirements.txt")
        print("   2. Check internet connection (for model downloads)")
        print("   3. Ensure you have enough disk space and memory")
        print("   4. Try running: python setup.py")
    
    return all_passed


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️  Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        sys.exit(1)
