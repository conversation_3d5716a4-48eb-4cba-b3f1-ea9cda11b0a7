"""
Enhanced core search engine that combines frame extraction, CLIP matching, and result processing.
Features advanced error handling, performance optimization, and memory management.
"""

import os
import time
import gc
import psutil
import logging
import threading
from typing import List, Tuple, Optional, Dict, Any, Union
from pathlib import Path
from concurrent.futures import Thr<PERSON><PERSON><PERSON>Executor, as_completed
import numpy as np

try:
    # Try relative imports first (when used as package)
    from .frame_extraction import FrameExtractor
    from ..models.clip_model import CLIPMatcher
    from .video_slicing import VideoClipper
    from ..config import VideoSearchConfig, print_config_info
    from .advanced_matching import AdvancedMatcher
except ImportError:
    # Fall back to absolute imports (when run directly)
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from utils.frame_extraction import FrameExtractor
    from models.clip_model import CLIPMatcher
    from utils.video_slicing import VideoClipper
    from config import VideoSearchConfig, print_config_info
    try:
        from utils.advanced_matching import AdvancedMatcher
    except ImportError:
        AdvancedMatcher = None

# Setup logging
logger = logging.getLogger(__name__)


class VideoSearchEngine:
    """Enhanced main search engine for AI-powered video content search with advanced features."""

    def __init__(self,
                 clip_model_name: str = "openai/clip-vit-base-patch32",
                 output_dir: str = "static/output_clips",
                 frame_interval: int = 30,
                 max_frames: Optional[int] = None,
                 target_resolution: Optional[Tuple[int, int]] = (512, 384),
                 quality_factor: float = 0.8,
                 use_chunked_processing: bool = True,
                 chunk_size: int = 200,
                 adaptive_config: bool = True,
                 enable_parallel_processing: bool = True,
                 max_workers: Optional[int] = None,
                 enable_memory_monitoring: bool = True,
                 memory_limit_mb: Optional[int] = None):
        """
        Initialize the enhanced video search engine.

        Args:
            clip_model_name: CLIP model to use for matching
            output_dir: Directory for output clips and thumbnails
            frame_interval: Extract every N frames
            max_frames: Maximum frames to extract (None for no limit)
            target_resolution: Resize frames to this resolution for memory efficiency
            quality_factor: Frame compression quality (0.1-1.0)
            use_chunked_processing: Use chunked processing for large videos
            chunk_size: Number of frames to process at once
            adaptive_config: Use adaptive configuration based on video size
            enable_parallel_processing: Enable parallel processing for better performance
            max_workers: Maximum number of worker threads (None for auto)
            enable_memory_monitoring: Enable memory usage monitoring
            memory_limit_mb: Memory limit in MB (None for auto)
        """
        self.clip_model_name = clip_model_name
        self.output_dir = output_dir
        self.use_chunked_processing = use_chunked_processing
        self.chunk_size = chunk_size
        self.adaptive_config = adaptive_config
        self.enable_parallel_processing = enable_parallel_processing
        self.max_workers = max_workers or min(4, os.cpu_count() or 1)
        self.enable_memory_monitoring = enable_memory_monitoring

        # Memory management
        self._max_cache_size_mb = memory_limit_mb or VideoSearchConfig.MAX_CACHE_SIZE_MB
        self._current_memory_usage = 0
        self._memory_lock = threading.Lock()

        # Store default settings
        self.default_settings = {
            'frame_interval': frame_interval,
            'max_frames': max_frames,
            'target_resolution': target_resolution,
            'quality_factor': quality_factor
        }

        # Initialize components with default settings
        self.frame_extractor = FrameExtractor(
            every_n_frames=frame_interval,
            max_frames=max_frames,
            target_resolution=target_resolution,
            quality_factor=quality_factor
        )
        self.clip_matcher = None  # Lazy loading
        self.video_clipper = VideoClipper(output_dir=output_dir)
        self.advanced_matcher = None  # Lazy loading for advanced matching

        # Cache for extracted frames and video info
        self._frame_cache = {}
        self._video_info_cache = {}
        self._cache_access_times = {}  # For LRU cache management

        # Performance tracking
        self._performance_stats = {
            'total_searches': 0,
            'total_processing_time': 0,
            'cache_hits': 0,
            'cache_misses': 0,
            'memory_cleanups': 0
        }

        # Error tracking
        self._error_history = []

        # Create output directory
        os.makedirs(self.output_dir, exist_ok=True)

        logger.info(f"VideoSearchEngine initialized with {self.max_workers} workers")
    
    def _ensure_clip_matcher(self):
        """Lazy load the CLIP matcher to avoid loading model unnecessarily."""
        if self.clip_matcher is None:
            try:
                logger.info("Loading CLIP model...")
                print("🤖 Loading CLIP model...")
                self.clip_matcher = CLIPMatcher(model_name=self.clip_model_name)
                logger.info("CLIP model loaded successfully")
            except Exception as e:
                error_msg = f"Failed to load CLIP model: {e}"
                logger.error(error_msg)
                self._error_history.append(error_msg)
                raise RuntimeError(error_msg) from e

    def _ensure_advanced_matcher(self):
        """Lazy load the advanced matcher for improved accuracy."""
        if self.advanced_matcher is None and AdvancedMatcher is not None:
            try:
                logger.info("Loading advanced matcher...")
                self.advanced_matcher = AdvancedMatcher()
                logger.info("Advanced matcher loaded successfully")
            except Exception as e:
                logger.warning(f"Failed to load advanced matcher: {e}")
                # Advanced matcher is optional, so we don't raise an error
    
    def search_video(self,
                    video_path: str,
                    query: str,
                    similarity_threshold: float = 0.2,
                    top_k: Optional[int] = 20,
                    create_clips: bool = False,
                    clip_duration: float = 3.0,
                    create_thumbnails: bool = True,
                    thumbnail_size: Tuple[int, int] = (480, 360),
                    image_quality: int = 90,
                    use_advanced_matching: bool = True,
                    progress_callback: Optional[callable] = None) -> Dict[str, Any]:
        """
        Enhanced search for content in a video based on text query with comprehensive error handling.

        Args:
            video_path: Path to the video file
            query: Text query to search for
            similarity_threshold: Minimum similarity score
            top_k: Maximum number of results to return
            create_clips: Whether to create video clips
            clip_duration: Duration of each clip in seconds
            create_thumbnails: Whether to create thumbnail images
            thumbnail_size: Size of thumbnail images (width, height)
            image_quality: JPEG quality for thumbnail images (70-100)
            use_advanced_matching: Use advanced matching for better accuracy
            progress_callback: Optional callback function for progress updates

        Returns:
            Dictionary containing search results and metadata
        """
        start_time = time.time()
        search_id = f"search_{int(start_time)}"

        # Update performance stats
        self._performance_stats['total_searches'] += 1

        try:
            logger.info(f"Starting video search [{search_id}] for: '{query}' in {video_path}")
            print(f"🔍 Starting video search for: '{query}'")
            print(f"📹 Video: {os.path.basename(video_path)}")

            # Validate inputs
            self._validate_search_inputs(video_path, query, similarity_threshold, top_k)

            if progress_callback:
                progress_callback("Validating video file...", 0.1)

            # Get video info with error handling
            try:
                video_info = self._get_video_info(video_path)
                logger.info(f"Video info: {video_info['duration_seconds']:.1f}s, {video_info['fps']:.1f}fps")
            except Exception as e:
                error_msg = f"Failed to read video information: {e}"
                logger.error(error_msg)
                return self._create_error_result(query, video_path, error_msg, start_time)

            if progress_callback:
                progress_callback("Extracting frames...", 0.2)

            # Extract frames with error handling and memory monitoring
            try:
                frames = self._get_frames(video_path)
                if not frames:
                    error_msg = "No frames could be extracted from video"
                    logger.warning(error_msg)
                    return self._create_error_result(query, video_path, error_msg, start_time, video_info)

                logger.info(f"Extracted {len(frames)} frames for analysis")
            except Exception as e:
                error_msg = f"Frame extraction failed: {e}"
                logger.error(error_msg)
                return self._create_error_result(query, video_path, error_msg, start_time, video_info)

            if progress_callback:
                progress_callback("Loading AI models...", 0.3)

            # Ensure models are loaded
            try:
                self._ensure_clip_matcher()
                if use_advanced_matching:
                    self._ensure_advanced_matcher()
            except Exception as e:
                error_msg = f"Model loading failed: {e}"
                logger.error(error_msg)
                return self._create_error_result(query, video_path, error_msg, start_time, video_info)

            if progress_callback:
                progress_callback("Searching for matches...", 0.5)

            # Search for matching frames with enhanced error handling
            try:
                matches = self._search_frames_with_fallback(
                    frames, query, similarity_threshold, top_k, use_advanced_matching
                )
                logger.info(f"Found {len(matches)} matches above threshold {similarity_threshold}")
            except Exception as e:
                error_msg = f"Frame matching failed: {e}"
                logger.error(error_msg)
                return self._create_error_result(query, video_path, error_msg, start_time, video_info)

            # Process results with parallel processing if enabled
            clips = []
            thumbnails = []

            if matches:
                if progress_callback:
                    progress_callback("Processing results...", 0.7)

                try:
                    if self.enable_parallel_processing and (create_clips or create_thumbnails):
                        clips, thumbnails = self._process_results_parallel(
                            video_path, matches, create_clips, create_thumbnails,
                            clip_duration, thumbnail_size, image_quality, query
                        )
                    else:
                        clips, thumbnails = self._process_results_sequential(
                            video_path, matches, create_clips, create_thumbnails,
                            clip_duration, thumbnail_size, image_quality, query
                        )
                except Exception as e:
                    logger.warning(f"Result processing failed: {e}")
                    # Continue with empty results rather than failing completely

            if progress_callback:
                progress_callback("Finalizing results...", 0.9)

            processing_time = time.time() - start_time
            self._performance_stats['total_processing_time'] += processing_time

            # Prepare comprehensive results
            results = self._create_success_result(
                query, video_path, video_info, matches, clips, thumbnails,
                processing_time, frames, similarity_threshold, search_id
            )

            logger.info(f"Search [{search_id}] completed successfully in {processing_time:.2f}s")
            print(f"✅ Search completed in {processing_time:.2f} seconds")
            print(f"🎯 Found {len(matches)} matches")

            if progress_callback:
                progress_callback("Complete!", 1.0)

            return results

        except Exception as e:
            error_msg = f"Unexpected error during search: {e}"
            logger.error(error_msg, exc_info=True)
            self._error_history.append(error_msg)
            return self._create_error_result(query, video_path, error_msg, start_time)
    
    def batch_search(self, 
                    video_path: str, 
                    queries: List[str],
                    similarity_threshold: float = 0.2,
                    create_clips: bool = True) -> Dict[str, Dict[str, Any]]:
        """
        Search for multiple queries in the same video.
        
        Args:
            video_path: Path to the video file
            queries: List of text queries
            similarity_threshold: Minimum similarity score
            create_clips: Whether to create video clips
            
        Returns:
            Dictionary mapping queries to their results
        """
        start_time = time.time()
        
        print(f"Starting batch search for {len(queries)} queries")
        
        # Get video info and frames once
        video_info = self._get_video_info(video_path)
        frames = self._get_frames(video_path)
        
        if not frames:
            return {}
        
        # Ensure CLIP matcher is loaded
        self._ensure_clip_matcher()
        
        # Batch search
        batch_matches = self.clip_matcher.batch_search(
            frames=frames,
            queries=queries,
            threshold=similarity_threshold
        )
        
        # Process results for each query
        results = {}
        for query, matches in batch_matches.items():
            clips = []
            if matches and create_clips:
                clips = self.video_clipper.create_clips_from_matches(
                    video_path=video_path,
                    matches=matches,
                    query_name=query
                )
            
            results[query] = {
                'query': query,
                'video_path': video_path,
                'video_info': video_info,
                'matches': [
                    {
                        'frame_index': frame_idx,
                        'timestamp': timestamp,
                        'similarity_score': score,
                        'time_formatted': self._format_time(timestamp)
                    }
                    for frame_idx, _, timestamp, score in matches
                ],
                'clips': clips,
                'stats': {
                    'matches_found': len(matches),
                    'clips_created': len(clips)
                }
            }
        
        processing_time = time.time() - start_time
        print(f"Batch search completed in {processing_time:.2f} seconds")
        
        return results
    
    def _get_frames(self, video_path: str) -> List[Tuple[int, np.ndarray, float]]:
        """Get frames from video with caching and adaptive configuration."""
        if video_path not in self._frame_cache:
            print("Extracting frames from video...")

            # Get video info for adaptive configuration
            video_info = self._get_video_info(video_path)

            # Use adaptive configuration if enabled
            if self.adaptive_config:
                config = VideoSearchConfig.get_adaptive_config(video_path, video_info)
                print_config_info(config)

                # Update frame extractor with adaptive settings
                self.frame_extractor = FrameExtractor(
                    every_n_frames=config['frame_interval'],
                    max_frames=config['max_frames'],
                    target_resolution=config['target_resolution'],
                    quality_factor=config['quality_factor']
                )

                # Update CLIP matcher batch size if loaded
                if self.clip_matcher is not None:
                    self.clip_matcher.batch_size = config['batch_size']

                use_chunked = config['use_chunked_processing']
            else:
                # Use existing settings
                video_duration = video_info['duration_seconds']
                estimated_frames = int(video_duration * video_info['fps'] / self.frame_extractor.every_n_frames)
                use_chunked = self.use_chunked_processing and (video_duration > 300 or estimated_frames > 1000)

            # Extract frames
            if use_chunked:
                print(f"Using chunked processing for large video...")
                frames = self.frame_extractor.extract_frames_chunked(video_path, chunk_size=self.chunk_size)
            else:
                frames = self.frame_extractor.extract_frames(video_path)

            # Check memory usage and manage cache
            self._manage_cache_memory()
            self._frame_cache[video_path] = frames

        return self._frame_cache[video_path]
    
    def _get_video_info(self, video_path: str) -> Dict[str, Any]:
        """Get video info with caching."""
        if video_path not in self._video_info_cache:
            self._video_info_cache[video_path] = self.frame_extractor.get_video_info(video_path)
        return self._video_info_cache[video_path]
    
    def _format_time(self, seconds: float) -> str:
        """Format time in seconds to MM:SS format."""
        minutes = int(seconds // 60)
        seconds = int(seconds % 60)
        return f"{minutes:02d}:{seconds:02d}"
    
    def clear_cache(self):
        """Clear the frame cache to free memory."""
        self._frame_cache.clear()
        self._video_info_cache.clear()
        print("Cache cleared")
    
    def cleanup_outputs(self):
        """Clean up output directory."""
        self.video_clipper.cleanup_output_dir()
        print("Output directory cleaned")
    
    def _manage_cache_memory(self):
        """Manage cache memory usage to prevent out-of-memory errors."""
        total_memory_mb = self._estimate_cache_memory_usage()

        if total_memory_mb > self._max_cache_size_mb:
            print(f"Cache memory usage ({total_memory_mb:.1f} MB) exceeds limit ({self._max_cache_size_mb} MB)")
            print("Clearing oldest cached videos...")

            # Clear cache starting with oldest entries
            cache_items = list(self._frame_cache.items())
            for video_path, frames in cache_items:
                del self._frame_cache[video_path]
                if video_path in self._video_info_cache:
                    del self._video_info_cache[video_path]

                total_memory_mb = self._estimate_cache_memory_usage()
                if total_memory_mb <= self._max_cache_size_mb * 0.7:  # Keep 30% buffer
                    break

            print(f"Cache cleared. Current memory usage: {total_memory_mb:.1f} MB")

    def _estimate_cache_memory_usage(self) -> float:
        """Estimate memory usage of cached frames in MB."""
        total_bytes = 0
        for frames in self._frame_cache.values():
            for _, frame_array, _ in frames:
                total_bytes += frame_array.nbytes
        return total_bytes / (1024 * 1024)

    def get_cache_info(self) -> Dict[str, Any]:
        """Get information about cached data."""
        memory_usage_mb = self._estimate_cache_memory_usage()
        return {
            'cached_videos': list(self._frame_cache.keys()),
            'total_cached_frames': sum(len(frames) for frames in self._frame_cache.values()),
            'memory_usage_mb': memory_usage_mb,
            'memory_limit_mb': self._max_cache_size_mb,
            'clip_matcher_loaded': self.clip_matcher is not None
        }

    def _validate_search_inputs(self, video_path: str, query: str,
                               similarity_threshold: float, top_k: Optional[int]):
        """Validate search inputs and raise appropriate errors."""
        if not video_path or not isinstance(video_path, str):
            raise ValueError("video_path must be a non-empty string")

        if not os.path.exists(video_path):
            raise FileNotFoundError(f"Video file not found: {video_path}")

        if not query or not isinstance(query, str):
            raise ValueError("query must be a non-empty string")

        if not 0.0 <= similarity_threshold <= 1.0:
            raise ValueError("similarity_threshold must be between 0.0 and 1.0")

        if top_k is not None and (not isinstance(top_k, int) or top_k <= 0):
            raise ValueError("top_k must be a positive integer or None")

        # Check file size
        file_size_mb = os.path.getsize(video_path) / (1024 * 1024)
        if file_size_mb > 10000:  # 10GB limit
            logger.warning(f"Large video file detected: {file_size_mb:.1f}MB")

    def _create_error_result(self, query: str, video_path: str, error_msg: str,
                           start_time: float, video_info: Optional[Dict] = None) -> Dict[str, Any]:
        """Create a standardized error result."""
        return {
            'query': query,
            'video_path': video_path,
            'video_info': video_info,
            'matches': [],
            'clips': [],
            'thumbnails': [],
            'processing_time': time.time() - start_time,
            'error': error_msg,
            'success': False
        }

    def _create_success_result(self, query: str, video_path: str, video_info: Dict,
                             matches: List, clips: List, thumbnails: List,
                             processing_time: float, frames: List,
                             similarity_threshold: float, search_id: str) -> Dict[str, Any]:
        """Create a standardized success result."""
        return {
            'query': query,
            'video_path': video_path,
            'video_info': video_info,
            'matches': [
                {
                    'frame_index': frame_idx,
                    'timestamp': timestamp,
                    'similarity_score': score,
                    'time_formatted': self._format_time(timestamp)
                }
                for frame_idx, _, timestamp, score in matches
            ],
            'clips': clips,
            'thumbnails': [{'path': path, 'score': score} for path, score in thumbnails],
            'processing_time': processing_time,
            'stats': {
                'total_frames_extracted': len(frames),
                'matches_found': len(matches),
                'clips_created': len(clips),
                'thumbnails_created': len(thumbnails),
                'similarity_threshold': similarity_threshold,
                'search_id': search_id,
                'cache_hits': self._performance_stats['cache_hits'],
                'cache_misses': self._performance_stats['cache_misses']
            },
            'success': True
        }

    def _search_frames_with_fallback(self, frames: List, query: str,
                                   similarity_threshold: float, top_k: Optional[int],
                                   use_advanced_matching: bool) -> List:
        """Search frames with fallback to basic matching if advanced fails."""
        try:
            # Try advanced matching first if enabled and available
            if use_advanced_matching and self.advanced_matcher:
                logger.info("Using advanced matching")
                return self.advanced_matcher.search_frames(
                    frames, query, similarity_threshold, top_k
                )
        except Exception as e:
            logger.warning(f"Advanced matching failed, falling back to basic: {e}")

        # Fall back to basic CLIP matching
        logger.info("Using basic CLIP matching")
        return self.clip_matcher.search_frames(
            frames=frames,
            query=query,
            threshold=similarity_threshold,
            top_k=top_k,
            use_advanced_matching=False
        )

    def _process_results_parallel(self, video_path: str, matches: List,
                                create_clips: bool, create_thumbnails: bool,
                                clip_duration: float, thumbnail_size: Tuple[int, int],
                                image_quality: int, query: str) -> Tuple[List, List]:
        """Process results using parallel processing."""
        clips = []
        thumbnails = []

        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            futures = []

            if create_clips:
                future = executor.submit(
                    self.video_clipper.create_clips_from_matches,
                    video_path, matches, clip_duration, query
                )
                futures.append(('clips', future))

            if create_thumbnails:
                future = executor.submit(
                    self.video_clipper.extract_frame_thumbnails,
                    matches, thumbnail_size, image_quality, query, True
                )
                futures.append(('thumbnails', future))

            # Collect results
            for result_type, future in futures:
                try:
                    result = future.result(timeout=300)  # 5 minute timeout
                    if result_type == 'clips':
                        clips = result
                    elif result_type == 'thumbnails':
                        thumbnails = result
                except Exception as e:
                    logger.error(f"Parallel processing failed for {result_type}: {e}")

        return clips, thumbnails

    def _process_results_sequential(self, video_path: str, matches: List,
                                  create_clips: bool, create_thumbnails: bool,
                                  clip_duration: float, thumbnail_size: Tuple[int, int],
                                  image_quality: int, query: str) -> Tuple[List, List]:
        """Process results sequentially."""
        clips = []
        thumbnails = []

        if create_clips:
            print("🎬 Creating video clips...")
            clips = self.video_clipper.create_clips_from_matches(
                video_path=video_path,
                matches=matches,
                clip_duration=clip_duration,
                query_name=query
            )

        if create_thumbnails:
            print("🖼️ Creating thumbnails...")
            thumbnails = self.video_clipper.extract_frame_thumbnails(
                matches,
                thumbnail_size=thumbnail_size,
                image_quality=image_quality,
                query=query,
                extract_objects_only=True
            )

        return clips, thumbnails

    def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics."""
        return {
            **self._performance_stats,
            'average_processing_time': (
                self._performance_stats['total_processing_time'] /
                max(1, self._performance_stats['total_searches'])
            ),
            'cache_hit_rate': (
                self._performance_stats['cache_hits'] /
                max(1, self._performance_stats['cache_hits'] + self._performance_stats['cache_misses'])
            ),
            'error_count': len(self._error_history)
        }

    def get_error_history(self) -> List[str]:
        """Get recent error history."""
        return self._error_history[-10:]  # Return last 10 errors


def create_search_engine(clip_model_name: str = "openai/clip-vit-base-patch32",
                        output_dir: str = "static/output_clips",
                        frame_interval: int = 30) -> VideoSearchEngine:
    """
    Factory function to create a video search engine.
    
    Args:
        clip_model_name: CLIP model to use
        output_dir: Output directory for clips
        frame_interval: Frame extraction interval
        
    Returns:
        VideoSearchEngine instance
    """
    return VideoSearchEngine(
        clip_model_name=clip_model_name,
        output_dir=output_dir,
        frame_interval=frame_interval
    )
