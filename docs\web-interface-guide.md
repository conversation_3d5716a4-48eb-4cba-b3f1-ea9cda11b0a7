# 🌐 Web Interface Guide - Complete UI Walkthrough

This comprehensive guide covers every aspect of the AI-Powered Video Content Search web interface, from basic navigation to advanced features and customization options.

## 🚀 Getting Started

### **Accessing the Web Interface**
```bash
# Start the application
python main.py --web

# Or use launcher scripts
./launch_web.sh      # Linux/macOS
launch_web.bat       # Windows
```

**Default URL:** http://localhost:8501

### **Browser Compatibility**
- ✅ **Chrome 90+** (Recommended)
- ✅ **Firefox 88+**
- ✅ **Safari 14+**
- ✅ **Edge 90+**
- ⚠️ **Internet Explorer** (Not supported)

### **Interface Overview**
The web interface consists of 5 main tabs:
1. **📁 Video File Search** - Upload and search video files
2. **📹 Live Video Detection** - Real-time camera/stream processing
3. **📊 Batch Processing** - Multiple videos or queries
4. **📈 Analytics & History** - Performance metrics and search history
5. **⚙️ Advanced Settings** - Configuration and optimization

## 📁 Tab 1: Video File Search

### **File Upload Section**

**Upload Methods:**
- **Drag & Drop:** Drag video files directly onto the upload area
- **File Browser:** Click "Browse files" to select from file system
- **Multiple Files:** Upload multiple videos for batch processing

**Supported Formats:**
- **Primary:** MP4, AVI, MOV, MKV, WMV
- **Additional:** FLV, WebM, 3GP, OGV
- **Maximum Size:** 10GB per file (configurable)

**Upload Progress:**
- Real-time progress bar
- File size and upload speed display
- Cancel option during upload
- Error handling for failed uploads

### **Search Configuration Panel**

**Basic Settings:**
- **Search Query:** Natural language description
  - Placeholder text with examples
  - Query history dropdown
  - Suggested queries based on content
- **Similarity Threshold:** 0.1 - 0.5 slider
  - Visual indicator of precision vs recall
  - Recommended values for different use cases
- **Max Results:** 1 - 100 results limit
  - Performance impact indicator

**Advanced Options (Expandable):**
- **Frame Interval:** 1 - 120 frames
  - Speed vs thoroughness trade-off indicator
- **Processing Resolution:** Dropdown selection
  - Quality vs speed impact display
- **Create Video Clips:** Toggle with duration setting
- **Create Thumbnails:** Toggle with size options
- **Advanced Matching:** Enable/disable with explanation

### **Search Execution**

**Progress Tracking:**
- **Phase 1:** Video analysis and frame extraction
- **Phase 2:** AI model processing
- **Phase 3:** Results compilation and ranking
- **Real-time updates:** Frames processed, time elapsed, estimated completion

**Progress Indicators:**
- Overall progress bar (0-100%)
- Current phase description
- Processing speed (frames per second)
- Memory usage monitoring
- Cancel search option

### **Results Display**

**Results Overview:**
- **Summary Statistics:**
  - Total matches found
  - Processing time
  - Video duration and frame count
  - Average similarity score
- **Filter Options:**
  - Similarity score range slider
  - Timestamp range selector
  - Sort by score/timestamp

**Individual Results:**
- **Thumbnail Grid View:**
  - Hover for larger preview
  - Click for full-size modal
  - Similarity score overlay
  - Timestamp display
- **List View Option:**
  - Detailed information per result
  - Larger thumbnails
  - Additional metadata

**Result Actions:**
- **Download Individual Images:** Right-click or download button
- **Download All Results:** Bulk ZIP download
- **Create Video Clips:** Generate clips for selected results
- **Export Metadata:** JSON/CSV export of results data

### **Video Player Integration**

**Embedded Player:**
- **Jump to Timestamp:** Click result to seek video
- **Playback Controls:** Play, pause, seek, volume
- **Speed Control:** 0.25x to 2x playback speed
- **Fullscreen Mode:** Expand for detailed viewing

**Synchronized Results:**
- **Timeline Markers:** Visual indicators of matches on timeline
- **Auto-highlight:** Current result highlighted during playback
- **Quick Navigation:** Previous/next result buttons

## 📹 Tab 2: Live Video Detection

### **Source Selection**

**Camera Input:**
- **Device Selection:** Dropdown of available cameras
- **Camera Index:** Manual index input (0, 1, 2, etc.)
- **Resolution Settings:** Available resolutions for selected camera
- **Frame Rate:** FPS selection (15, 24, 30, 60)

**Stream Input:**
- **RTSP URL:** Text input with format examples
- **HTTP Stream:** Support for MJPEG and other HTTP streams
- **YouTube Live:** Direct YouTube live stream URLs
- **Connection Testing:** Test button to verify stream accessibility

**Stream Configuration:**
- **Buffer Size:** Adjust for latency vs stability
- **Reconnection:** Auto-reconnect on connection loss
- **Quality Settings:** Adaptive quality based on connection

### **Detection Settings**

**Search Parameters:**
- **Query Input:** Real-time search query
- **Detection Interval:** How often to analyze frames (0.1 - 5.0 seconds)
- **Confidence Threshold:** Minimum detection confidence (0.1 - 0.9)
- **Max Detections:** Maximum results to keep (5 - 50)

**Processing Options:**
- **Object Extraction:** Extract detected objects only
- **Save Detections:** Auto-save detected images
- **Timestamp Overlay:** Add timestamp to saved images
- **Detection Zones:** Define specific areas to monitor

### **Live Display**

**Video Feed:**
- **Real-time Stream:** Live video display
- **Detection Overlays:** Bounding boxes around detected objects
- **Confidence Scores:** Real-time confidence display
- **Frame Rate:** Current processing FPS

**Detection Results:**
- **Live Results Panel:** Scrolling list of recent detections
- **Thumbnail Gallery:** Grid of detected objects
- **Detection Counter:** Running count of total detections
- **Time-based Filtering:** Show detections from last N minutes

### **Control Panel**

**Playback Controls:**
- **Start Detection:** Begin real-time processing
- **Pause Detection:** Pause without losing session
- **Resume Detection:** Continue from pause
- **Stop Detection:** End session and save results

**Recording Options:**
- **Record Stream:** Save live stream to file
- **Record Detections:** Save only detection events
- **Screenshot:** Capture current frame
- **Export Session:** Save all detection data

## 📊 Tab 3: Batch Processing

### **Processing Modes**

**Mode 1: Multiple Videos, Single Query**
- **Video Upload:** Multiple file selection or drag-and-drop
- **Single Query:** One search term applied to all videos
- **Comparison View:** Side-by-side results comparison
- **Aggregate Statistics:** Combined results across all videos

**Mode 2: Single Video, Multiple Queries**
- **Video Upload:** Single video file
- **Query List:** Multiple search terms in sequence
- **Comprehensive Analysis:** Complete content breakdown
- **Category Results:** Organized by query type

**Mode 3: Multiple Videos, Multiple Queries**
- **Matrix Processing:** All combinations of videos and queries
- **Advanced Scheduling:** Queue management and prioritization
- **Resource Management:** Memory and processing optimization
- **Detailed Reporting:** Comprehensive results matrix

### **Queue Management**

**Job Queue:**
- **Job List:** Current and pending processing jobs
- **Priority Settings:** High, normal, low priority levels
- **Progress Tracking:** Individual job progress
- **Estimated Completion:** Time remaining for queue

**Queue Controls:**
- **Add Jobs:** Add new processing jobs to queue
- **Reorder Jobs:** Drag-and-drop priority adjustment
- **Cancel Jobs:** Remove jobs from queue
- **Pause Queue:** Pause all processing

### **Batch Results**

**Results Organization:**
- **Tabbed Results:** Separate tab for each video/query combination
- **Comparison Mode:** Side-by-side result comparison
- **Aggregate View:** Combined statistics and insights
- **Export Options:** Bulk export of all results

**Performance Analytics:**
- **Processing Times:** Time taken for each job
- **Resource Usage:** Memory and CPU utilization
- **Success Rates:** Percentage of successful matches
- **Quality Metrics:** Average similarity scores

## 📈 Tab 4: Analytics & History

### **Search History**

**History List:**
- **Recent Searches:** Chronological list of past searches
- **Search Details:** Query, video, settings, and results count
- **Quick Repeat:** One-click to repeat previous searches
- **History Export:** Export search history to CSV/JSON

**History Filters:**
- **Date Range:** Filter by search date
- **Video Name:** Filter by specific videos
- **Query Type:** Filter by search terms
- **Success Rate:** Filter by result quality

### **Performance Metrics**

**System Performance:**
- **Processing Speed:** Average frames per second
- **Memory Usage:** Current and peak memory consumption
- **GPU Utilization:** GPU usage statistics (if available)
- **Cache Performance:** Hit rates and efficiency metrics

**Search Analytics:**
- **Query Effectiveness:** Most successful query patterns
- **Threshold Optimization:** Optimal thresholds for different content
- **Processing Time Trends:** Performance over time
- **Error Analysis:** Common issues and failure patterns

### **Visualization Dashboard**

**Charts and Graphs:**
- **Processing Time Trends:** Line charts of performance over time
- **Query Success Rates:** Bar charts of query effectiveness
- **Memory Usage Patterns:** Area charts of resource consumption
- **Result Quality Distribution:** Histograms of similarity scores

**Interactive Elements:**
- **Zoom and Pan:** Detailed examination of chart data
- **Data Point Details:** Hover for specific information
- **Time Range Selection:** Focus on specific time periods
- **Export Charts:** Save charts as images or data

### **Reports Generation**

**Automated Reports:**
- **Daily Summary:** Automatic daily performance reports
- **Weekly Analysis:** Comprehensive weekly analytics
- **Custom Reports:** User-defined report parameters
- **Email Reports:** Scheduled email delivery (if configured)

**Report Formats:**
- **PDF Reports:** Professional formatted documents
- **Excel Exports:** Detailed data in spreadsheet format
- **JSON Data:** Raw data for further analysis
- **HTML Reports:** Web-viewable reports

## ⚙️ Tab 5: Advanced Settings

### **Model Configuration**

**AI Model Settings:**
- **CLIP Model Selection:** Dropdown of available models
  - Performance vs accuracy trade-offs
  - Model size and memory requirements
  - Download status and management
- **YOLO Model Selection:** Object detection model options
- **Device Selection:** CPU, CUDA, MPS, or auto-detection
- **Model Optimization:** Mixed precision, quantization options

**Model Management:**
- **Download Models:** Download additional model variants
- **Model Cache:** Manage downloaded model storage
- **Update Models:** Check for and install model updates
- **Model Performance:** Benchmark different models

### **Performance Tuning**

**Processing Settings:**
- **Batch Size:** Frames processed simultaneously
- **Worker Threads:** Number of parallel processing threads
- **Memory Limits:** Maximum memory usage thresholds
- **GPU Memory:** VRAM allocation and management

**Optimization Options:**
- **Adaptive Configuration:** Auto-adjust based on video properties
- **Chunked Processing:** Enable for large videos
- **Frame Caching:** Cache frequently accessed frames
- **Result Caching:** Cache search results for reuse

### **System Configuration**

**Storage Settings:**
- **Output Directory:** Location for saved results
- **Cache Directory:** Temporary file storage location
- **Log Directory:** Application log file location
- **Cleanup Settings:** Automatic cleanup of old files

**Network Settings:**
- **Proxy Configuration:** HTTP/HTTPS proxy settings
- **Download Settings:** Model download preferences
- **Stream Settings:** Network stream configuration
- **Security Settings:** SSL/TLS configuration

### **User Interface Customization**

**Appearance Settings:**
- **Theme Selection:** Light, dark, or auto themes
- **Color Scheme:** Custom color preferences
- **Font Size:** Interface text size adjustment
- **Layout Options:** Compact or expanded layouts

**Behavior Settings:**
- **Auto-save Settings:** Automatically save configuration changes
- **Default Values:** Set default values for common settings
- **Keyboard Shortcuts:** Customize hotkeys
- **Notification Preferences:** Alert and notification settings

### **Import/Export Configuration**

**Configuration Management:**
- **Export Settings:** Save current configuration to file
- **Import Settings:** Load configuration from file
- **Reset to Defaults:** Restore factory settings
- **Configuration Profiles:** Save and switch between different profiles

**Backup and Restore:**
- **Backup Configuration:** Create configuration backups
- **Restore Configuration:** Restore from backup
- **Migration Tools:** Transfer settings between installations
- **Version Compatibility:** Handle configuration version differences

## 🎯 Tips for Effective Interface Use

### **Navigation Best Practices**
- **Use Keyboard Shortcuts:** Learn common shortcuts for efficiency
- **Bookmark Frequently Used Settings:** Save common configurations
- **Organize Results:** Use folders and naming conventions
- **Monitor Performance:** Keep an eye on system resources

### **Workflow Optimization**
- **Start with Video File Search:** Learn basics before advanced features
- **Use Batch Processing:** For multiple similar tasks
- **Leverage History:** Repeat successful searches
- **Customize Settings:** Tailor interface to your needs

### **Troubleshooting Interface Issues**
- **Refresh Browser:** Clear temporary issues
- **Check Browser Console:** Look for JavaScript errors
- **Clear Cache:** Remove stored data causing conflicts
- **Try Different Browser:** Test compatibility issues

---

**Master the Interface:** With this comprehensive guide, you now have complete knowledge of every feature and option in the web interface. Practice with different content types to become proficient with all the tools available!
