# 🎬 Large Video Processing Guide

This guide explains how to efficiently process large video files with the AI-powered video search application.

## 🚀 **Optimizations for Large Videos**

The application now includes several optimizations specifically designed for handling large video files:

### 📊 **Adaptive Configuration**
- **Automatic Detection**: The system automatically detects video size and duration
- **Smart Settings**: Applies optimal settings based on file size and length
- **Memory Management**: Prevents out-of-memory errors with intelligent caching

### 🔧 **Processing Optimizations**

#### **1. Frame Extraction**
- **Adaptive Intervals**: Larger videos use higher frame intervals (extract fewer frames)
- **Resolution Scaling**: Automatically reduces resolution for memory efficiency
- **Compression**: JPEG compression reduces memory usage by 40-70%
- **Chunked Processing**: Processes large videos in smaller chunks

#### **2. Memory Management**
- **Cache Limits**: 2GB cache limit with automatic cleanup
- **Batch Size Reduction**: Smaller batches for large videos
- **GPU Memory**: Automatic GPU memory cleanup between batches

#### **3. Quality vs Speed Trade-offs**
- **Small Videos** (< 100MB): High quality, all frames
- **Medium Videos** (100-500MB): Balanced quality and speed
- **Large Videos** (> 500MB): Optimized for speed and memory

## 📋 **Configuration Presets**

### **Quality Preset** (Best for small videos)
```
Frame Interval: 15 (high precision)
Resolution: 720x540 (high quality)
Compression: None (quality_factor: 1.0)
Batch Size: 8 (fast processing)
```

### **Balanced Preset** (Default for medium videos)
```
Frame Interval: 30 (good precision)
Resolution: 512x384 (good quality)
Compression: Light (quality_factor: 0.8)
Batch Size: 4 (balanced)
```

### **Speed Preset** (Best for large videos)
```
Frame Interval: 60 (fast processing)
Resolution: 320x240 (memory efficient)
Compression: More (quality_factor: 0.6)
Batch Size: 2 (memory safe)
```

### **Memory Preset** (Extreme optimization)
```
Frame Interval: 90 (minimal frames)
Resolution: 256x192 (very small)
Compression: Heavy (quality_factor: 0.4)
Batch Size: 1 (minimal memory)
```

## 🖥️ **Web Interface Settings**

### **Large Video Settings Panel**
The web interface includes a dedicated panel for large video optimization:

1. **Frame Interval**: 15-120 frames
   - Lower = more accurate, slower
   - Higher = faster, less accurate

2. **Processing Resolution**:
   - High Quality (720x540)
   - Medium Quality (512x384) ⭐ Recommended
   - Low Quality (320x240)
   - Original Resolution

3. **Compression Quality**: 0.3-1.0
   - Lower = more compression, less memory
   - Higher = better quality, more memory

### **Memory Monitor**
- Real-time memory usage display
- Cache information
- Automatic cleanup suggestions

## 💻 **Command Line Options**

### **Basic Large Video Processing**
```bash
# Fast processing for large videos
python main.py --video large_video.mp4 --query "person walking" \
  --frame-interval 60 --resolution low --quality 0.6

# Memory optimized processing
python main.py --video huge_video.mp4 --query "red car" \
  --frame-interval 90 --resolution low --quality 0.4 --max-results 10
```

### **Advanced Options**
```bash
# Custom settings
python main.py --video video.mp4 --query "sunset" \
  --frame-interval 45 \
  --resolution medium \
  --quality 0.7 \
  --clips \
  --thumbnails \
  --max-results 15
```

## 📈 **Performance Guidelines**

### **File Size Recommendations**

| Video Size | Frame Interval | Resolution | Quality | Expected Time |
|------------|----------------|------------|---------|---------------|
| < 100MB    | 15-30         | High       | 1.0     | 30-60 sec     |
| 100-500MB  | 30-45         | Medium     | 0.8     | 1-3 min       |
| 500MB-2GB  | 45-60         | Low        | 0.6     | 2-5 min       |
| > 2GB      | 60-90         | Low        | 0.4     | 5-10 min      |

### **Duration Recommendations**

| Video Length | Frame Interval | Max Frames | Processing Time |
|--------------|----------------|------------|-----------------|
| < 5 min      | 15-30         | Unlimited  | 30-90 sec       |
| 5-30 min     | 30-60         | 1000       | 1-5 min         |
| 30-60 min    | 60-90         | 500        | 3-8 min         |
| > 60 min     | 90-120        | 300        | 5-15 min        |

## 🛠️ **Troubleshooting Large Videos**

### **Out of Memory Errors**
```
Solutions:
1. Increase frame interval (extract fewer frames)
2. Reduce processing resolution
3. Lower compression quality
4. Reduce max results
5. Clear cache before processing
```

### **Slow Processing**
```
Solutions:
1. Use "Speed" or "Memory" preset
2. Increase frame interval to 60-90
3. Use low resolution (320x240)
4. Disable clip and thumbnail generation
5. Reduce max results to 10-20
```

### **Poor Search Results**
```
Solutions:
1. Decrease frame interval (more frames)
2. Increase processing resolution
3. Improve compression quality
4. Try different search terms
5. Lower similarity threshold
```

## 🎯 **Best Practices**

### **For Very Large Videos (> 1GB)**
1. **Start with Speed preset**
2. **Use frame interval 60-90**
3. **Set max results to 10-20**
4. **Process in segments if needed**
5. **Monitor memory usage**

### **For Long Videos (> 1 hour)**
1. **Use adaptive configuration**
2. **Limit max frames to 300-500**
3. **Consider splitting into segments**
4. **Use specific time ranges**
5. **Clear cache between searches**

### **Memory Optimization**
1. **Close other applications**
2. **Use chunked processing**
3. **Enable compression**
4. **Monitor cache usage**
5. **Clear cache regularly**

## 📊 **Example Configurations**

### **4K Video (2GB, 30 minutes)**
```python
config = {
    "frame_interval": 90,
    "target_resolution": (320, 240),
    "quality_factor": 0.4,
    "max_frames": 300,
    "batch_size": 1
}
```

### **HD Video (500MB, 10 minutes)**
```python
config = {
    "frame_interval": 45,
    "target_resolution": (512, 384),
    "quality_factor": 0.7,
    "max_frames": 500,
    "batch_size": 2
}
```

### **Mobile Video (100MB, 5 minutes)**
```python
config = {
    "frame_interval": 30,
    "target_resolution": (720, 540),
    "quality_factor": 0.9,
    "max_frames": 1000,
    "batch_size": 4
}
```

## 🔍 **Monitoring and Debugging**

### **Memory Usage**
- Check cache info in web interface
- Monitor system memory usage
- Watch for memory warnings

### **Processing Speed**
- Note frame extraction time
- Monitor CLIP encoding speed
- Check overall processing time

### **Quality Assessment**
- Review search results accuracy
- Adjust settings based on results
- Test with different queries

---

**💡 Pro Tip**: Start with the "Balanced" preset and adjust based on your specific needs and hardware capabilities. The adaptive configuration will automatically optimize settings for most videos!

