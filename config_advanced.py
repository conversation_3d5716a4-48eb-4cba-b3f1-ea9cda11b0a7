"""
Advanced configuration management for AI-powered video content search.
Provides comprehensive settings management with file-based configuration.
"""

import os
import json
import logging
from typing import Dict, Any, Optional, Tuple
from pathlib import Path
from dataclasses import dataclass, asdict
from config import VideoSearchConfig, PRESETS


@dataclass
class ModelConfig:
    """Model configuration settings."""
    clip_model_name: str = "openai/clip-vit-base-patch32"
    yolo_model_name: str = "yolov8n.pt"
    device: str = "auto"  # auto, cpu, cuda
    model_cache_dir: str = "models/cache"
    enable_model_optimization: bool = True
    mixed_precision: bool = True


@dataclass
class ProcessingConfig:
    """Video processing configuration."""
    default_frame_interval: int = 30
    default_target_resolution: Tuple[int, int] = (512, 384)
    default_quality_factor: float = 0.8
    max_video_size_gb: float = 10.0
    supported_formats: list = None
    chunk_size: int = 200
    use_chunked_processing: bool = True
    enable_gpu_acceleration: bool = True
    
    def __post_init__(self):
        if self.supported_formats is None:
            self.supported_formats = [".mp4", ".avi", ".mov", ".mkv", ".wmv", ".flv", ".webm"]


@dataclass
class SearchConfig:
    """Search configuration settings."""
    default_similarity_threshold: float = 0.2
    advanced_similarity_threshold: float = 0.25
    default_max_results: int = 20
    default_clip_duration: float = 3.0
    enable_advanced_matching: bool = True
    enable_object_extraction: bool = True
    confidence_threshold: float = 0.5


@dataclass
class OutputConfig:
    """Output configuration settings."""
    output_dir: str = "static/output_clips"
    temp_dir: str = "temp_videos"
    test_output_dir: str = "test_output"
    default_image_quality: int = 90
    default_thumbnail_size: Tuple[int, int] = (480, 360)
    cleanup_on_exit: bool = True
    save_metadata: bool = True


@dataclass
class MemoryConfig:
    """Memory management configuration."""
    max_cache_size_mb: int = 2048
    enable_memory_monitoring: bool = True
    auto_cleanup_threshold: float = 0.8
    frame_cache_limit: int = 1000
    enable_garbage_collection: bool = True


@dataclass
class LiveDetectionConfig:
    """Live detection configuration."""
    detection_interval: float = 0.5
    max_live_results: int = 20
    enable_pause_resume: bool = True
    save_detections: bool = True
    detection_confidence_threshold: float = 0.5
    enable_object_tracking: bool = False


@dataclass
class WebInterfaceConfig:
    """Web interface configuration."""
    streamlit_port: int = 8501
    streamlit_host: str = "localhost"
    enable_file_upload: bool = True
    max_upload_size_mb: int = 500
    enable_live_detection: bool = True
    theme: str = "light"
    enable_dark_mode: bool = True


@dataclass
class LoggingConfig:
    """Logging configuration."""
    level: str = "INFO"
    log_file: str = "logs/app.log"
    enable_file_logging: bool = True
    enable_console_logging: bool = True
    max_log_size_mb: int = 100
    backup_count: int = 5


class AdvancedConfig:
    """Advanced configuration manager with file persistence."""
    
    def __init__(self, config_file: str = "config.json"):
        """Initialize configuration manager."""
        self.config_file = config_file
        self.models = ModelConfig()
        self.processing = ProcessingConfig()
        self.search = SearchConfig()
        self.output = OutputConfig()
        self.memory = MemoryConfig()
        self.live_detection = LiveDetectionConfig()
        self.web_interface = WebInterfaceConfig()
        self.logging = LoggingConfig()
        
        # Load configuration from file if it exists
        self.load_config()
        
        # Create necessary directories
        self._create_directories()
        
        # Setup logging
        self._setup_logging()
    
    def load_config(self) -> bool:
        """Load configuration from JSON file."""
        if not os.path.exists(self.config_file):
            self.save_config()  # Create default config file
            return True
        
        try:
            with open(self.config_file, 'r') as f:
                config_data = json.load(f)
            
            # Update configuration objects
            if 'models' in config_data:
                self.models = ModelConfig(**config_data['models'])
            if 'processing' in config_data:
                self.processing = ProcessingConfig(**config_data['processing'])
            if 'search' in config_data:
                self.search = SearchConfig(**config_data['search'])
            if 'output' in config_data:
                self.output = OutputConfig(**config_data['output'])
            if 'memory' in config_data:
                self.memory = MemoryConfig(**config_data['memory'])
            if 'live_detection' in config_data:
                self.live_detection = LiveDetectionConfig(**config_data['live_detection'])
            if 'web_interface' in config_data:
                self.web_interface = WebInterfaceConfig(**config_data['web_interface'])
            if 'logging' in config_data:
                self.logging = LoggingConfig(**config_data['logging'])
            
            return True
            
        except Exception as e:
            print(f"Error loading config from {self.config_file}: {e}")
            return False
    
    def save_config(self) -> bool:
        """Save current configuration to JSON file."""
        try:
            config_data = {
                'models': asdict(self.models),
                'processing': asdict(self.processing),
                'search': asdict(self.search),
                'output': asdict(self.output),
                'memory': asdict(self.memory),
                'live_detection': asdict(self.live_detection),
                'web_interface': asdict(self.web_interface),
                'logging': asdict(self.logging)
            }
            
            with open(self.config_file, 'w') as f:
                json.dump(config_data, f, indent=2)
            
            return True
            
        except Exception as e:
            print(f"Error saving config to {self.config_file}: {e}")
            return False
    
    def _create_directories(self):
        """Create necessary directories."""
        directories = [
            self.output.output_dir,
            self.output.temp_dir,
            self.output.test_output_dir,
            self.models.model_cache_dir,
            os.path.dirname(self.logging.log_file) if self.logging.log_file else "logs"
        ]
        
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
    
    def _setup_logging(self):
        """Setup logging configuration."""
        if self.logging.enable_file_logging:
            logging.basicConfig(
                level=getattr(logging, self.logging.level.upper()),
                format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                handlers=[
                    logging.FileHandler(self.logging.log_file),
                    logging.StreamHandler() if self.logging.enable_console_logging else logging.NullHandler()
                ]
            )
    
    def get_video_config(self, video_path: str, video_info: Optional[Dict] = None) -> Dict[str, Any]:
        """Get optimized configuration for a specific video."""
        if video_info:
            return VideoSearchConfig.get_adaptive_config(video_path, video_info)
        else:
            return VideoSearchConfig.get_config_for_video(video_path)
    
    def get_preset_config(self, preset_name: str) -> Dict[str, Any]:
        """Get configuration for a named preset."""
        return PRESETS.get(preset_name, PRESETS["balanced"]).copy()
    
    def update_from_dict(self, config_dict: Dict[str, Any]):
        """Update configuration from dictionary."""
        for section, values in config_dict.items():
            if hasattr(self, section):
                section_obj = getattr(self, section)
                for key, value in values.items():
                    if hasattr(section_obj, key):
                        setattr(section_obj, key, value)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary."""
        return {
            'models': asdict(self.models),
            'processing': asdict(self.processing),
            'search': asdict(self.search),
            'output': asdict(self.output),
            'memory': asdict(self.memory),
            'live_detection': asdict(self.live_detection),
            'web_interface': asdict(self.web_interface),
            'logging': asdict(self.logging)
        }
    
    def print_config(self):
        """Print current configuration in a readable format."""
        print("\n🔧 Advanced Configuration Settings")
        print("=" * 50)
        
        sections = [
            ("Models", self.models),
            ("Processing", self.processing),
            ("Search", self.search),
            ("Output", self.output),
            ("Memory", self.memory),
            ("Live Detection", self.live_detection),
            ("Web Interface", self.web_interface),
            ("Logging", self.logging)
        ]
        
        for section_name, section_obj in sections:
            print(f"\n📋 {section_name}:")
            for key, value in asdict(section_obj).items():
                print(f"   {key}: {value}")
        
        print("=" * 50)


# Global advanced configuration instance
advanced_config = AdvancedConfig()

# Export commonly used settings for backward compatibility
CLIP_MODEL_NAME = advanced_config.models.clip_model_name
YOLO_MODEL_NAME = advanced_config.models.yolo_model_name
DEFAULT_FRAME_INTERVAL = advanced_config.processing.default_frame_interval
DEFAULT_TARGET_RESOLUTION = advanced_config.processing.default_target_resolution
DEFAULT_QUALITY_FACTOR = advanced_config.processing.default_quality_factor
DEFAULT_SIMILARITY_THRESHOLD = advanced_config.search.default_similarity_threshold
DEFAULT_MAX_RESULTS = advanced_config.search.default_max_results
DEFAULT_CLIP_DURATION = advanced_config.search.default_clip_duration
OUTPUT_DIR = advanced_config.output.output_dir
TEMP_DIR = advanced_config.output.temp_dir
MAX_CACHE_SIZE_MB = advanced_config.memory.max_cache_size_mb
CHUNK_SIZE = advanced_config.processing.chunk_size
LIVE_DETECTION_INTERVAL = advanced_config.live_detection.detection_interval
MAX_LIVE_RESULTS = advanced_config.live_detection.max_live_results
STREAMLIT_PORT = advanced_config.web_interface.streamlit_port
STREAMLIT_HOST = advanced_config.web_interface.streamlit_host
