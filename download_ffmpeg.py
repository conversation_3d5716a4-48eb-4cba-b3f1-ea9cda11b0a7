#!/usr/bin/env python3
"""
Automatic FFmpeg downloader for AI-Powered Video Content Search.
Downloads and sets up FFmpeg locally if not found in system.
"""

import os
import sys
import zipfile
import urllib.request
from pathlib import Path
import subprocess

def download_ffmpeg():
    """Download FFmpeg automatically."""
    
    print("📥 Downloading FFmpeg...")
    
    # FFmpeg download URL (Windows essentials build)
    ffmpeg_url = "https://www.gyan.dev/ffmpeg/builds/packages/ffmpeg-6.0-essentials_build.zip"
    
    # Create ffmpeg directory
    ffmpeg_dir = Path("ffmpeg")
    ffmpeg_dir.mkdir(exist_ok=True)
    
    # Download path
    zip_path = ffmpeg_dir / "ffmpeg.zip"
    
    try:
        # Download FFmpeg
        print("   Downloading from: https://www.gyan.dev/ffmpeg/builds/")
        urllib.request.urlretrieve(ffmpeg_url, zip_path)
        print("   ✅ Download completed")
        
        # Extract ZIP
        print("   📂 Extracting...")
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            zip_ref.extractall(ffmpeg_dir)
        
        # Find ffmpeg.exe in extracted files
        for root, dirs, files in os.walk(ffmpeg_dir):
            if 'ffmpeg.exe' in files:
                source_ffmpeg = Path(root) / 'ffmpeg.exe'
                target_ffmpeg = ffmpeg_dir / 'ffmpeg.exe'
                
                # Copy to main ffmpeg directory
                import shutil
                shutil.copy2(source_ffmpeg, target_ffmpeg)
                print(f"   ✅ FFmpeg extracted to: {target_ffmpeg}")
                break
        
        # Clean up
        zip_path.unlink()
        print("   🧹 Cleaned up temporary files")
        
        # Test FFmpeg
        test_ffmpeg = ffmpeg_dir / 'ffmpeg.exe'
        if test_ffmpeg.exists():
            result = subprocess.run([str(test_ffmpeg), '-version'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                print("   ✅ FFmpeg is working correctly")
                return True
            else:
                print("   ❌ FFmpeg downloaded but not working")
                return False
        else:
            print("   ❌ FFmpeg.exe not found after extraction")
            return False
            
    except Exception as e:
        print(f"   ❌ Download failed: {e}")
        print("\n💡 Manual download instructions:")
        print("1. Go to: https://www.gyan.dev/ffmpeg/builds/")
        print("2. Download 'ffmpeg-release-essentials.zip'")
        print("3. Extract ffmpeg.exe to: ffmpeg/ffmpeg.exe")
        return False

def main():
    """Main download function."""
    print("📥 FFmpeg Auto-Downloader")
    print("=" * 40)
    
    # Check if FFmpeg already exists
    local_ffmpeg = Path("ffmpeg/ffmpeg.exe")
    if local_ffmpeg.exists():
        print("✅ FFmpeg already exists locally")
        return True
    
    # Check if FFmpeg is in PATH
    import shutil
    if shutil.which('ffmpeg'):
        print("✅ FFmpeg found in system PATH")
        return True
    
    # Download FFmpeg
    print("FFmpeg not found. Downloading...")
    return download_ffmpeg()

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 FFmpeg setup complete!")
        print("🚀 Run: python setup_ffmpeg.py")
    sys.exit(0 if success else 1)
