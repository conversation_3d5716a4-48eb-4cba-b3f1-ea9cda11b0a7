# 📹 Live Video Detection Guide

## 🚀 **Real-Time Object Detection with Your Camera!**

The AI-powered video search application now includes **Live Video Detection** - use your webcam or any video source to detect objects in real-time as they appear in front of the camera!

## ✨ **What's New: Live Detection Features**

### **📹 Real-Time Detection:**
- **Webcam Integration**: Use your computer's camera for live detection
- **Video File Playback**: Process video files in real-time
- **Instant Results**: See detections as objects appear
- **Object Extraction**: Get cropped images of detected objects in real-time

### **🎯 Smart Detection:**
- **Advanced AI Matching**: Same accuracy improvements as video file search
- **Configurable Intervals**: Detect every 0.1-2.0 seconds
- **Confidence Thresholds**: Adjust sensitivity for your needs
- **Multiple Objects**: Detect and extract multiple objects simultaneously

## 🎛️ **How to Use Live Detection**

### **🌐 Web Interface (Recommended):**

1. **Open the Application**
   - Go to http://localhost:8501
   - Click the **"📹 Live Video Detection"** tab

2. **Choose Video Source**
   - **Webcam**: Select camera index (0 for default camera)
   - **Video File**: Upload a video file for real-time processing

3. **Configure Detection Settings**
   - **Search Query**: "person", "red car", "dog", etc.
   - **Detection Threshold**: 0.25-0.3 recommended
   - **Detection Interval**: 0.5 seconds (adjust for speed vs accuracy)

4. **Start Detection**
   - Click **"🚀 Start Detection"**
   - See live video feed with real-time detection
   - View extracted objects as they're detected

5. **Monitor Results**
   - **Live Feed**: See current camera view
   - **Detection Status**: Real-time detection indicators
   - **Recent Detections**: Gallery of recently detected objects

### **💻 Command Line Interface:**

```bash
# Basic live detection
python main.py --live "person" --camera 0

# Advanced live detection
python main.py --live "red car" --camera 0 --threshold 0.25 --detection-interval 0.3

# Use external camera
python main.py --live "dog" --camera 1 --threshold 0.2
```

## 🎯 **Use Cases and Examples**

### **🏠 Home Security Monitoring:**
```bash
# Detect people at your door
python main.py --live "person" --threshold 0.3

# Monitor for vehicles
python main.py --live "car" --threshold 0.25
```

### **🐕 Pet Monitoring:**
```bash
# Watch for your dog
python main.py --live "dog" --threshold 0.2

# Monitor multiple pets
python main.py --live "animal" --threshold 0.2
```

### **🚗 Traffic Analysis:**
```bash
# Count vehicles
python main.py --live "car" --threshold 0.3

# Detect specific vehicle types
python main.py --live "truck" --threshold 0.25
```

### **🎮 Interactive Demos:**
```bash
# Detect objects you hold up to camera
python main.py --live "bottle" --threshold 0.2

# Color-based detection
python main.py --live "red object" --threshold 0.25
```

## ⚙️ **Settings and Configuration**

### **🎛️ Detection Settings:**

**Detection Threshold (0.1-0.5):**
- **0.15-0.2**: Very sensitive, more false positives
- **0.25-0.3**: Balanced, recommended for most use cases
- **0.3-0.4**: Conservative, fewer false positives

**Detection Interval (0.1-2.0 seconds):**
- **0.1-0.3**: Very responsive, higher CPU usage
- **0.5**: Balanced, good for most applications
- **1.0-2.0**: Less responsive, lower CPU usage

**Camera Settings:**
- **Camera Index 0**: Default/built-in camera
- **Camera Index 1+**: External USB cameras
- **Resolution**: Automatically set to 640x480 for optimal performance

### **📊 Performance Optimization:**

**For Real-Time Performance:**
- Use detection interval 0.3-0.5 seconds
- Set threshold to 0.25-0.3
- Close other applications using camera
- Ensure good lighting conditions

**For High Accuracy:**
- Use detection interval 0.1-0.2 seconds
- Set threshold to 0.3-0.4
- Use advanced matching (enabled by default)
- Ensure objects are clearly visible

## 🔧 **Technical Details**

### **🏗️ Architecture:**
- **Multi-threaded Design**: Separate threads for capture and detection
- **Queue-based Processing**: Smooth real-time performance
- **Memory Management**: Efficient handling of video frames
- **Error Recovery**: Automatic fallback for camera issues

### **🤖 AI Models Used:**
- **CLIP**: Text-to-image matching for object recognition
- **YOLOv5**: Object detection and localization (when available)
- **Advanced Matching**: Multi-query and negative filtering
- **Object Extraction**: Smart cropping of detected objects

### **📱 Supported Cameras:**
- **Built-in Webcams**: Laptop and desktop cameras
- **USB Cameras**: External webcams and USB cameras
- **Video Files**: Any supported video format (MP4, AVI, MOV, etc.)

## 🎉 **Real-World Examples**

### **Example 1: Pet Monitoring**
**Setup**: Point camera at pet area, search for "dog"
**Result**: Real-time detection when your dog enters the frame
**Use**: Automatic pet activity logging

### **Example 2: Security Monitoring**
**Setup**: Camera at entrance, search for "person"
**Result**: Instant alerts when people approach
**Use**: Home security and visitor detection

### **Example 3: Object Recognition Demo**
**Setup**: Hold objects in front of camera, search for specific items
**Result**: Real-time identification of objects you show
**Use**: Educational demos and object learning

### **Example 4: Traffic Monitoring**
**Setup**: Camera facing street, search for "car" or "truck"
**Result**: Count and classify vehicles in real-time
**Use**: Traffic analysis and vehicle counting

## 🔧 **Troubleshooting**

### **Camera Issues:**
```
Problem: Camera not detected
Solutions:
- Check camera index (try 0, 1, 2)
- Ensure camera is not used by other applications
- Check camera permissions
- Try different USB port (for external cameras)
```

### **Performance Issues:**
```
Problem: Slow or laggy detection
Solutions:
- Increase detection interval (0.5-1.0 seconds)
- Close other applications
- Ensure good lighting
- Lower detection threshold slightly
```

### **Detection Issues:**
```
Problem: Objects not detected
Solutions:
- Lower threshold (0.2-0.25)
- Improve lighting conditions
- Ensure objects are clearly visible
- Try more specific queries ("red car" vs "car")
```

### **False Positives:**
```
Problem: Detecting wrong objects
Solutions:
- Increase threshold (0.3-0.4)
- Use more specific queries
- Enable advanced matching
- Improve camera positioning
```

## 💡 **Pro Tips**

### **🎯 For Best Results:**
1. **Good Lighting**: Ensure adequate lighting for clear object visibility
2. **Stable Camera**: Mount camera securely to avoid motion blur
3. **Clear Background**: Simple backgrounds improve detection accuracy
4. **Specific Queries**: Use "red car" instead of just "vehicle"
5. **Optimal Distance**: Objects should be clearly visible, not too far or close

### **⚡ Performance Tips:**
1. **Close Other Apps**: Free up camera and CPU resources
2. **Adjust Interval**: Balance between responsiveness and performance
3. **Monitor CPU Usage**: Increase interval if CPU usage is too high
4. **Use Wired Connection**: For external cameras, use USB 3.0 if available

### **🔍 Detection Tips:**
1. **Start Conservative**: Begin with threshold 0.3, lower if needed
2. **Test Different Queries**: Try variations of your search terms
3. **Check Recent Detections**: Review the detection history for patterns
4. **Adjust Based on Environment**: Indoor vs outdoor may need different settings

## 🎊 **Success Stories**

### **Home Security User:**
*"I set up live detection for 'person' at my front door. Now I get instant notifications when someone approaches, and I can see exactly who it is!"*

### **Pet Owner:**
*"Monitoring my cats with 'cat' detection is amazing. I can see when they're active and even get cute photos automatically extracted!"*

### **Researcher:**
*"Using live detection for 'bird' in my backyard feeder setup. I'm building an automatic bird species catalog!"*

---

**📹 Live Video Detection brings AI-powered object recognition to real-time video streams - perfect for security, monitoring, research, and interactive demonstrations!**
