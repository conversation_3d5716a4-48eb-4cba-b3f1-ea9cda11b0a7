[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "ai-video-search"
version = "2.0.0"
description = "Professional AI-powered video content search using CLIP, YOLOv8, and computer vision"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "AI Video Search Team", email = "<EMAIL>"}
]
maintainers = [
    {name = "AI Video Search Team", email = "<EMAIL>"}
]
keywords = ["ai", "video", "search", "clip", "computer-vision", "object-detection", "streamlit"]
classifiers = [
    "Development Status :: 5 - Production/Stable",
    "Intended Audience :: Developers",
    "Intended Audience :: Science/Research", 
    "Intended Audience :: End Users/Desktop",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Multimedia :: Video",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Topic :: Scientific/Engineering :: Image Recognition",
]
requires-python = ">=3.8"
dependencies = [
    "torch>=1.12.0",
    "torchvision>=0.13.0",
    "transformers>=4.20.0",
    "opencv-python>=4.6.0",
    "moviepy>=1.0.3",
    "streamlit>=1.25.0",
    "Pillow>=9.0.0",
    "ffmpeg-python>=0.2.0",
    "ultralytics>=8.0.0",
    "numpy>=1.21.0",
    "scipy>=1.8.0",
    "matplotlib>=3.5.0",
    "requests>=2.28.0",
    "tqdm>=4.64.0",
    "yt-dlp>=2023.1.6",
]

[project.optional-dependencies]
dev = [
    "pytest>=6.0",
    "pytest-cov>=2.0",
    "black>=21.0",
    "flake8>=3.8",
    "mypy>=0.800",
    "pre-commit>=2.0",
    "pytest-mock>=3.0",
    "pytest-asyncio>=0.20.0",
]
gpu = [
    "torch[cuda]",
    "torchvision[cuda]",
]
streaming = [
    "yt-dlp>=2023.1.6",
    "pafy>=0.5.5",
]
all = [
    "ai-video-search[dev,gpu,streaming]"
]

[project.urls]
Homepage = "https://github.com/yourusername/ai-video-search"
Documentation = "https://ai-video-search.readthedocs.io/"
Repository = "https://github.com/yourusername/ai-video-search.git"
"Bug Tracker" = "https://github.com/yourusername/ai-video-search/issues"
Changelog = "https://github.com/yourusername/ai-video-search/blob/main/CHANGELOG.md"

[project.scripts]
ai-video-search = "main:main"
avs = "main:main"

[tool.setuptools]
packages = ["app", "models", "utils"]
include-package-data = true

[tool.setuptools.package-data]
"*" = ["*.md", "*.txt", "*.yml", "*.yaml", "*.json"]

[tool.black]
line-length = 88
target-version = ['py38']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[tool.pytest.ini_options]
minversion = "6.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]

[tool.coverage.run]
source = ["app", "models", "utils"]
omit = [
    "*/tests/*",
    "*/test_*",
    "setup.py",
    "*/venv/*",
    "*/__pycache__/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
