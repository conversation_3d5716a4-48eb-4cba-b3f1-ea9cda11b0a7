# 🚀 Quick Start Guide

Get up and running with AI-powered video search in just a few minutes!

## 📋 Prerequisites

- Python 3.8 or higher
- At least 4GB RAM (8GB+ recommended)
- Internet connection (for downloading AI models)

## ⚡ Installation

### Option 1: Automatic Setup (Recommended)

```bash
# Run the setup script
python setup.py
```

### Option 2: Manual Setup

```bash
# Install dependencies
pip install -r requirements.txt

# Create directories
mkdir -p static/output_clips temp_videos test_output
```

## 🧪 Test Installation

```bash
# Run tests to verify everything works
python test_application.py
```

## 🌐 Launch Web Interface

```bash
# Start the web application
python main.py --web
```

Then open your browser to: **http://localhost:8501**

## 📱 Using the Web Interface

1. **Upload Video**: Click "Browse files" and select a video (MP4, AVI, MOV, etc.)
2. **Enter Search Query**: Type what you're looking for (e.g., "red car", "person walking")
3. **Adjust Settings**: Use the sidebar to customize similarity threshold and output options
4. **Search**: Click "🔍 Search Video" and wait for results
5. **View Results**: Browse matching frames, video clips, and thumbnails

## 💻 Command Line Usage

```bash
# Basic search
python main.py --video path/to/video.mp4 --query "red cap"

# Advanced search with clips
python main.py --video video.mp4 --query "person walking" --clips --thumbnails

# Get video information
python main.py --info video.mp4

# Help
python main.py --help
```

## 🎯 Example Searches

Try these queries to get started:

### Objects
- "red car"
- "blue shirt" 
- "laptop computer"
- "coffee cup"

### People & Actions
- "person walking"
- "child playing"
- "someone talking"
- "people dancing"

### Scenes & Environment
- "sunset"
- "trees and grass"
- "city street"
- "indoor room"

### Colors & Attributes
- "something red"
- "bright colors"
- "dark scene"
- "outdoor lighting"

## ⚙️ Settings Guide

### Similarity Threshold
- **0.1-0.15**: Very loose matching (more results, less accurate)
- **0.2-0.25**: Balanced matching (recommended)
- **0.3-0.4**: Strict matching (fewer results, more accurate)

### Frame Interval
- **15-30**: High precision (more frames analyzed)
- **30-60**: Balanced (default)
- **60-120**: Fast processing (fewer frames)

## 🔧 Troubleshooting

### Common Issues

**"No matches found"**
- Lower the similarity threshold
- Try different search terms
- Check if the content actually exists in the video

**"CUDA out of memory"**
- The app will automatically fall back to CPU
- Close other applications to free GPU memory

**"Video format not supported"**
- Convert video to MP4 using online converters or FFmpeg
- Supported formats: MP4, AVI, MOV, MKV, WMV

**Slow processing**
- Increase frame interval (analyze fewer frames)
- Use shorter video clips for testing
- Ensure you have adequate RAM

### Performance Tips

1. **For long videos**: Increase frame interval to 60-120
2. **For better accuracy**: Decrease frame interval to 15-30
3. **For faster results**: Disable clip and thumbnail generation
4. **For GPU acceleration**: Ensure PyTorch with CUDA is installed

## 📊 Expected Performance

### Processing Times (approximate)
- **1-minute video**: 15-30 seconds
- **5-minute video**: 1-2 minutes  
- **10-minute video**: 2-4 minutes

### Hardware Requirements
- **Minimum**: 4GB RAM, CPU-only
- **Recommended**: 8GB+ RAM, GPU with 4GB+ VRAM
- **Optimal**: 16GB+ RAM, GPU with 8GB+ VRAM

## 🆘 Getting Help

If you encounter issues:

1. **Check the console output** for error messages
2. **Run the test script**: `python test_application.py`
3. **Try with a shorter video** first
4. **Check your internet connection** (for model downloads)
5. **Verify all dependencies are installed**: `pip list`

## 🎉 You're Ready!

Your AI video search application is now ready to use. Start by uploading a short video and searching for simple objects like "person" or "car" to get familiar with the interface.

**Happy searching! 🎬✨**
