{"timestamp": "2025-06-22 18:21:37", "platform": "Windows", "python_version": "3.12.2", "test_duration": 502.5542001724243, "total_tests": 9, "passed_tests": 7, "success_rate": 77.77777777777779, "test_results": {"python_environment": true, "core_dependencies": true, "gpu_support": false, "system_dependencies": false, "file_structure": true, "core_functionality": true, "configuration": true, "memory_usage": true, "sample_functionality": true}, "errors": ["Missing system dependency: ffmpeg"], "warnings": ["Not using virtual environment", "No GPU acceleration", "No test video available"]}