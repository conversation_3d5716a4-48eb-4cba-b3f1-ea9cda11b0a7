#!/usr/bin/env python3
"""
Advanced Installation Manager for AI-Powered Video Content Search.
Provides comprehensive installation options, dependency management, and system optimization.
"""

import os
import sys
import subprocess
import platform
import shutil
import json
import urllib.request
import zipfile
import tarfile
import tempfile
import hashlib
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class Colors:
    """Enhanced ANSI color codes for terminal output."""
    HEADER = '\033[95m'
    OKBLUE = '\033[94m'
    OKCYAN = '\033[96m'
    OKGREEN = '\033[92m'
    WARNING = '\033[93m'
    FAIL = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'
    PURPLE = '\033[35m'
    YELLOW = '\033[33m'


class SystemInfo:
    """Comprehensive system information gathering."""
    
    def __init__(self):
        self.platform = platform.system()
        self.architecture = platform.machine()
        self.python_version = sys.version_info
        self.is_admin = self._check_admin_privileges()
        self.package_managers = self._detect_package_managers()
        self.virtual_env = self._detect_virtual_environment()
        
    def _check_admin_privileges(self) -> bool:
        """Check if running with administrator privileges."""
        try:
            if self.platform == "Windows":
                import ctypes
                return ctypes.windll.shell32.IsUserAnAdmin()
            else:
                return os.geteuid() == 0
        except:
            return False
    
    def _detect_package_managers(self) -> Dict[str, bool]:
        """Detect available package managers."""
        managers = {
            'pip': shutil.which('pip') is not None,
            'conda': shutil.which('conda') is not None,
            'poetry': shutil.which('poetry') is not None,
            'pipenv': shutil.which('pipenv') is not None,
        }
        
        # Platform-specific package managers
        if self.platform == "Windows":
            managers.update({
                'chocolatey': shutil.which('choco') is not None,
                'scoop': shutil.which('scoop') is not None,
                'winget': shutil.which('winget') is not None,
            })
        elif self.platform == "Darwin":  # macOS
            managers.update({
                'homebrew': shutil.which('brew') is not None,
                'macports': shutil.which('port') is not None,
            })
        else:  # Linux
            managers.update({
                'apt': shutil.which('apt') is not None,
                'yum': shutil.which('yum') is not None,
                'dnf': shutil.which('dnf') is not None,
                'pacman': shutil.which('pacman') is not None,
                'zypper': shutil.which('zypper') is not None,
                'snap': shutil.which('snap') is not None,
                'flatpak': shutil.which('flatpak') is not None,
            })
        
        return managers
    
    def _detect_virtual_environment(self) -> Dict[str, Any]:
        """Detect virtual environment information."""
        return {
            'in_venv': hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix),
            'conda_env': 'CONDA_DEFAULT_ENV' in os.environ,
            'conda_env_name': os.environ.get('CONDA_DEFAULT_ENV', None),
            'virtual_env': os.environ.get('VIRTUAL_ENV', None),
            'pipenv_active': os.environ.get('PIPENV_ACTIVE', None),
        }
    
    def print_system_info(self):
        """Print comprehensive system information."""
        print(f"{Colors.HEADER}{Colors.BOLD}🖥️  System Information{Colors.ENDC}")
        print(f"Platform: {self.platform} {self.architecture}")
        print(f"Python: {self.python_version.major}.{self.python_version.minor}.{self.python_version.micro}")
        print(f"Admin Privileges: {'Yes' if self.is_admin else 'No'}")
        
        if self.virtual_env['in_venv']:
            print(f"Virtual Environment: Active")
            if self.virtual_env['conda_env']:
                print(f"Conda Environment: {self.virtual_env['conda_env_name']}")
            if self.virtual_env['virtual_env']:
                print(f"Virtual Env Path: {self.virtual_env['virtual_env']}")
        else:
            print(f"Virtual Environment: None (Global Python)")
        
        print(f"\n📦 Available Package Managers:")
        for manager, available in self.package_managers.items():
            status = f"{Colors.OKGREEN}✅" if available else f"{Colors.FAIL}❌"
            print(f"   {status} {manager}{Colors.ENDC}")


class DependencyManager:
    """Advanced dependency management and installation."""
    
    def __init__(self, system_info: SystemInfo):
        self.system_info = system_info
        self.required_system_deps = {
            'git': 'Git version control system',
            'ffmpeg': 'FFmpeg multimedia framework',
        }
        self.optional_system_deps = {
            'nvidia-smi': 'NVIDIA GPU management (for CUDA support)',
            'rocm-smi': 'AMD GPU management (for ROCm support)',
        }
    
    def check_system_dependencies(self) -> Tuple[List[str], List[str]]:
        """Check system dependencies and return missing required and optional deps."""
        missing_required = []
        missing_optional = []
        
        print(f"\n{Colors.OKBLUE}🔧 Checking System Dependencies{Colors.ENDC}")
        
        # Check required dependencies
        for dep, description in self.required_system_deps.items():
            if shutil.which(dep):
                print(f"   {Colors.OKGREEN}✅ {dep}{Colors.ENDC} - {description}")
            else:
                print(f"   {Colors.FAIL}❌ {dep}{Colors.ENDC} - {description}")
                missing_required.append(dep)
        
        # Check optional dependencies
        for dep, description in self.optional_system_deps.items():
            if shutil.which(dep):
                print(f"   {Colors.OKGREEN}✅ {dep}{Colors.ENDC} - {description}")
            else:
                print(f"   {Colors.WARNING}⚠️  {dep}{Colors.ENDC} - {description} (optional)")
                missing_optional.append(dep)
        
        return missing_required, missing_optional
    
    def install_system_dependencies(self, missing_deps: List[str]) -> bool:
        """Attempt to install missing system dependencies."""
        if not missing_deps:
            return True
        
        print(f"\n{Colors.YELLOW}📦 Installing System Dependencies{Colors.ENDC}")
        
        if self.system_info.platform == "Windows":
            return self._install_windows_deps(missing_deps)
        elif self.system_info.platform == "Darwin":
            return self._install_macos_deps(missing_deps)
        else:
            return self._install_linux_deps(missing_deps)
    
    def _install_windows_deps(self, deps: List[str]) -> bool:
        """Install dependencies on Windows."""
        if self.system_info.package_managers['chocolatey']:
            return self._install_with_chocolatey(deps)
        elif self.system_info.package_managers['scoop']:
            return self._install_with_scoop(deps)
        elif self.system_info.package_managers['winget']:
            return self._install_with_winget(deps)
        else:
            print(f"{Colors.WARNING}⚠️  No package manager found. Please install manually:{Colors.ENDC}")
            self._print_manual_install_instructions_windows(deps)
            return False
    
    def _install_macos_deps(self, deps: List[str]) -> bool:
        """Install dependencies on macOS."""
        if self.system_info.package_managers['homebrew']:
            return self._install_with_homebrew(deps)
        elif self.system_info.package_managers['macports']:
            return self._install_with_macports(deps)
        else:
            print(f"{Colors.WARNING}⚠️  No package manager found. Please install Homebrew:{Colors.ENDC}")
            print("   /bin/bash -c \"$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)\"")
            return False
    
    def _install_linux_deps(self, deps: List[str]) -> bool:
        """Install dependencies on Linux."""
        if self.system_info.package_managers['apt']:
            return self._install_with_apt(deps)
        elif self.system_info.package_managers['dnf']:
            return self._install_with_dnf(deps)
        elif self.system_info.package_managers['yum']:
            return self._install_with_yum(deps)
        elif self.system_info.package_managers['pacman']:
            return self._install_with_pacman(deps)
        elif self.system_info.package_managers['zypper']:
            return self._install_with_zypper(deps)
        else:
            print(f"{Colors.WARNING}⚠️  No supported package manager found.{Colors.ENDC}")
            self._print_manual_install_instructions_linux(deps)
            return False
    
    def _install_with_chocolatey(self, deps: List[str]) -> bool:
        """Install using Chocolatey on Windows."""
        try:
            cmd = ['choco', 'install', '-y'] + deps
            subprocess.run(cmd, check=True)
            print(f"{Colors.OKGREEN}✅ Dependencies installed with Chocolatey{Colors.ENDC}")
            return True
        except subprocess.CalledProcessError as e:
            print(f"{Colors.FAIL}❌ Chocolatey installation failed: {e}{Colors.ENDC}")
            return False
    
    def _install_with_homebrew(self, deps: List[str]) -> bool:
        """Install using Homebrew on macOS."""
        try:
            cmd = ['brew', 'install'] + deps
            subprocess.run(cmd, check=True)
            print(f"{Colors.OKGREEN}✅ Dependencies installed with Homebrew{Colors.ENDC}")
            return True
        except subprocess.CalledProcessError as e:
            print(f"{Colors.FAIL}❌ Homebrew installation failed: {e}{Colors.ENDC}")
            return False
    
    def _install_with_apt(self, deps: List[str]) -> bool:
        """Install using APT on Debian/Ubuntu."""
        try:
            # Update package list first
            subprocess.run(['sudo', 'apt', 'update'], check=True)
            cmd = ['sudo', 'apt', 'install', '-y'] + deps
            subprocess.run(cmd, check=True)
            print(f"{Colors.OKGREEN}✅ Dependencies installed with APT{Colors.ENDC}")
            return True
        except subprocess.CalledProcessError as e:
            print(f"{Colors.FAIL}❌ APT installation failed: {e}{Colors.ENDC}")
            return False
    
    def _print_manual_install_instructions_windows(self, deps: List[str]):
        """Print manual installation instructions for Windows."""
        print(f"\n{Colors.OKCYAN}💡 Windows Manual Installation:{Colors.ENDC}")
        for dep in deps:
            if dep == 'git':
                print("   - Git: https://git-scm.com/download/win")
            elif dep == 'ffmpeg':
                print("   - FFmpeg: https://ffmpeg.org/download.html#build-windows")
        print("\n   Or install a package manager:")
        print("   - Chocolatey: https://chocolatey.org/install")
        print("   - Scoop: https://scoop.sh/")
    
    def create_python_environment(self) -> bool:
        """Create and setup Python virtual environment."""
        print(f"\n{Colors.OKBLUE}🐍 Setting up Python Environment{Colors.ENDC}")
        
        if self.system_info.virtual_env['in_venv']:
            print(f"   {Colors.OKGREEN}✅ Already in virtual environment{Colors.ENDC}")
            return True
        
        # Create virtual environment
        venv_path = Path("venv")
        if not venv_path.exists():
            try:
                print("   Creating virtual environment...")
                subprocess.run([sys.executable, "-m", "venv", "venv"], check=True)
                print(f"   {Colors.OKGREEN}✅ Virtual environment created{Colors.ENDC}")
            except subprocess.CalledProcessError as e:
                print(f"   {Colors.FAIL}❌ Failed to create virtual environment: {e}{Colors.ENDC}")
                return False
        
        # Provide activation instructions
        if self.system_info.platform == "Windows":
            activate_cmd = "venv\\Scripts\\activate"
        else:
            activate_cmd = "source venv/bin/activate"
        
        print(f"\n   {Colors.YELLOW}⚠️  Please activate the virtual environment:{Colors.ENDC}")
        print(f"   {activate_cmd}")
        print(f"   Then run this installer again.")
        
        return False  # Return False to indicate manual activation needed
    
    def install_python_dependencies(self, upgrade_pip: bool = True) -> bool:
        """Install Python dependencies with enhanced error handling."""
        print(f"\n{Colors.OKBLUE}📦 Installing Python Dependencies{Colors.ENDC}")
        
        try:
            if upgrade_pip:
                print("   Upgrading pip...")
                subprocess.run([
                    sys.executable, "-m", "pip", "install", "--upgrade", "pip"
                ], check=True, capture_output=True)
            
            # Check if requirements.txt exists
            if not Path("requirements.txt").exists():
                print(f"   {Colors.FAIL}❌ requirements.txt not found{Colors.ENDC}")
                return False
            
            print("   Installing requirements...")
            result = subprocess.run([
                sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                print(f"   {Colors.OKGREEN}✅ All dependencies installed successfully{Colors.ENDC}")
                return True
            else:
                print(f"   {Colors.FAIL}❌ Installation failed:{Colors.ENDC}")
                print(f"   {result.stderr}")
                return False
                
        except subprocess.CalledProcessError as e:
            print(f"   {Colors.FAIL}❌ Error installing dependencies: {e}{Colors.ENDC}")
            return False
    
    def verify_installation(self) -> Tuple[bool, List[str]]:
        """Verify that all required packages are installed."""
        print(f"\n{Colors.OKBLUE}🧪 Verifying Installation{Colors.ENDC}")
        
        required_packages = [
            ('torch', 'PyTorch'),
            ('torchvision', 'TorchVision'),
            ('transformers', 'Transformers'),
            ('cv2', 'OpenCV'),
            ('streamlit', 'Streamlit'),
            ('PIL', 'Pillow'),
            ('moviepy.editor', 'MoviePy'),
            ('ultralytics', 'Ultralytics'),
            ('numpy', 'NumPy'),
            ('scipy', 'SciPy'),
            ('matplotlib', 'Matplotlib'),
            ('sklearn', 'Scikit-learn'),
            ('pandas', 'Pandas'),
            ('plotly', 'Plotly'),
        ]
        
        failed_imports = []
        
        for module, name in required_packages:
            try:
                __import__(module)
                print(f"   {Colors.OKGREEN}✅ {name}{Colors.ENDC}")
            except ImportError as e:
                failed_imports.append(f"{name}: {e}")
                print(f"   {Colors.FAIL}❌ {name}: {e}{Colors.ENDC}")
        
        success = len(failed_imports) == 0
        if success:
            print(f"\n   {Colors.OKGREEN}🎉 All packages verified successfully!{Colors.ENDC}")
        else:
            print(f"\n   {Colors.FAIL}❌ {len(failed_imports)} packages failed verification{Colors.ENDC}")
        
        return success, failed_imports


class AdvancedInstaller:
    """Main advanced installer class."""
    
    def __init__(self):
        self.system_info = SystemInfo()
        self.dependency_manager = DependencyManager(self.system_info)
        self.installation_log = []
    
    def print_header(self):
        """Print enhanced installation header."""
        print(f"{Colors.HEADER}{Colors.BOLD}")
        print("=" * 80)
        print("🎬 AI-Powered Video Content Search - Advanced Installation Manager")
        print("=" * 80)
        print(f"{Colors.ENDC}")
        self.system_info.print_system_info()
    
    def run_interactive_installation(self) -> bool:
        """Run interactive installation with user choices."""
        self.print_header()
        
        print(f"\n{Colors.OKCYAN}🚀 Starting Interactive Installation{Colors.ENDC}")
        
        # Step 1: Check system dependencies
        missing_required, missing_optional = self.dependency_manager.check_system_dependencies()
        
        if missing_required:
            print(f"\n{Colors.WARNING}⚠️  Missing required dependencies: {', '.join(missing_required)}{Colors.ENDC}")
            
            if self.system_info.is_admin:
                choice = input("Attempt automatic installation? (y/N): ").lower().strip()
                if choice == 'y':
                    if not self.dependency_manager.install_system_dependencies(missing_required):
                        print(f"{Colors.FAIL}❌ Automatic installation failed. Please install manually.{Colors.ENDC}")
                        return False
                else:
                    print(f"{Colors.FAIL}❌ Please install required dependencies manually.{Colors.ENDC}")
                    return False
            else:
                print(f"{Colors.FAIL}❌ Administrator privileges required for automatic installation.{Colors.ENDC}")
                print("Please install dependencies manually or run as administrator.")
                return False
        
        # Step 2: Setup Python environment
        if not self.system_info.virtual_env['in_venv']:
            choice = input("\nCreate virtual environment? (Y/n): ").lower().strip()
            if choice != 'n':
                if not self.dependency_manager.create_python_environment():
                    return False
        
        # Step 3: Install Python dependencies
        if not self.dependency_manager.install_python_dependencies():
            return False
        
        # Step 4: Verify installation
        success, failed_imports = self.dependency_manager.verify_installation()
        if not success:
            print(f"\n{Colors.FAIL}❌ Installation verification failed:{Colors.ENDC}")
            for failure in failed_imports:
                print(f"   - {failure}")
            return False
        
        # Step 5: Create configuration and launcher scripts
        self._create_additional_files()
        
        print(f"\n{Colors.OKGREEN}{Colors.BOLD}🎉 Installation completed successfully!{Colors.ENDC}")
        self._print_next_steps()
        
        return True
    
    def _create_additional_files(self):
        """Create additional configuration and launcher files."""
        print(f"\n{Colors.OKBLUE}📝 Creating Additional Files{Colors.ENDC}")
        
        # Create launcher scripts
        self._create_launcher_scripts()
        
        # Create configuration file
        self._create_config_file()
        
        # Create desktop shortcuts (if requested)
        choice = input("Create desktop shortcuts? (y/N): ").lower().strip()
        if choice == 'y':
            self._create_desktop_shortcuts()
    
    def _create_launcher_scripts(self):
        """Create enhanced launcher scripts."""
        scripts = {
            'launch_web.bat' if self.system_info.platform == "Windows" else 'launch_web.sh': [
                "#!/bin/bash" if self.system_info.platform != "Windows" else "@echo off",
                "echo Starting AI Video Search Web Interface...",
                "python main.py --web" if self.system_info.platform == "Windows" else "python3 main.py --web",
                "pause" if self.system_info.platform == "Windows" else ""
            ],
            'launch_live.bat' if self.system_info.platform == "Windows" else 'launch_live.sh': [
                "#!/bin/bash" if self.system_info.platform != "Windows" else "@echo off",
                "echo Starting AI Video Search Live Detection...",
                'set /p query="Enter search query: "' if self.system_info.platform == "Windows" else 'read -p "Enter search query: " query',
                'python main.py --live --query "%query%"' if self.system_info.platform == "Windows" else 'python3 main.py --live --query "$query"',
                "pause" if self.system_info.platform == "Windows" else ""
            ]
        }
        
        for script_name, lines in scripts.items():
            with open(script_name, 'w') as f:
                for line in lines:
                    if line:  # Skip empty lines
                        f.write(line + '\n')
            
            # Make executable on Unix systems
            if self.system_info.platform != "Windows":
                os.chmod(script_name, 0o755)
            
            print(f"   {Colors.OKGREEN}✅ {script_name}{Colors.ENDC}")
    
    def _create_config_file(self):
        """Create initial configuration file."""
        try:
            from config_advanced import AdvancedConfig
            config = AdvancedConfig()
            config.save_config()
            print(f"   {Colors.OKGREEN}✅ config.json{Colors.ENDC}")
        except Exception as e:
            print(f"   {Colors.WARNING}⚠️  Could not create config file: {e}{Colors.ENDC}")
    
    def _create_desktop_shortcuts(self):
        """Create desktop shortcuts (platform-specific)."""
        if self.system_info.platform == "Windows":
            self._create_windows_shortcuts()
        elif self.system_info.platform == "Darwin":
            self._create_macos_shortcuts()
        else:
            self._create_linux_shortcuts()
    
    def _print_next_steps(self):
        """Print next steps after successful installation."""
        print(f"\n{Colors.OKCYAN}🚀 Next Steps:{Colors.ENDC}")
        print("   1. Test the installation:")
        print("      python test_application.py")
        print("   2. Start the web interface:")
        print("      python main.py --web")
        print("   3. Try command line search:")
        print("      python main.py --video video.mp4 --query 'red car'")
        print("   4. Try live detection:")
        print("      python main.py --live --query 'person'")
        print("   5. Read the documentation:")
        print("      docs/COMPLETE_USER_GUIDE.md")


def main():
    """Main installation function."""
    installer = AdvancedInstaller()
    
    try:
        success = installer.run_interactive_installation()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print(f"\n{Colors.WARNING}⚠️  Installation interrupted by user{Colors.ENDC}")
        sys.exit(1)
    except Exception as e:
        print(f"\n{Colors.FAIL}❌ Unexpected error: {e}{Colors.ENDC}")
        logger.exception("Installation failed with unexpected error")
        sys.exit(1)


if __name__ == "__main__":
    main()
