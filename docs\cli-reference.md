# 💻 Command Line Interface Reference

Complete reference for the AI-Powered Video Content Search command line interface, including all options, examples, and advanced usage patterns.

## 🚀 Quick Start

### **Basic Usage**
```bash
# Simple video search
python main.py --video video.mp4 --query "red car"

# Start web interface
python main.py --web

# Live detection
python main.py --live --query "person"
```

### **Getting Help**
```bash
# Show all available options
python main.py --help

# Show version information
python main.py --version

# Show system information
python main.py --system-info
```

## 📋 Command Structure

### **General Syntax**
```bash
python main.py [MODE] [OPTIONS] [ARGUMENTS]
```

**Modes:**
- `--video` - Video file search mode
- `--web` - Web interface mode  
- `--live` - Live detection mode
- `--batch` - Batch processing mode

## 🎬 Video Search Mode

### **Basic Video Search**
```bash
python main.py --video VIDEO_PATH --query "SEARCH_QUERY"
```

### **Required Arguments**
- `--video PATH` - Path to video file
- `--query TEXT` - Search query in natural language

### **Optional Arguments**

#### **Search Parameters**
```bash
--threshold FLOAT          # Similarity threshold (0.1-0.5, default: 0.2)
--max-results INT          # Maximum results to return (default: 20)
--frame-interval INT       # Process every N frames (default: 30)
--resolution WIDTHxHEIGHT  # Processing resolution (default: auto)
```

#### **Output Options**
```bash
--output-dir PATH          # Output directory (default: static/output_clips)
--create-clips             # Generate video clips of matches
--create-thumbnails        # Extract thumbnail images (default: true)
--clip-duration FLOAT      # Duration of video clips in seconds (default: 3.0)
--thumbnail-size WIDTHxHEIGHT  # Thumbnail dimensions (default: 480x360)
--image-quality INT        # JPEG quality 1-100 (default: 90)
```

#### **Processing Options**
```bash
--device DEVICE           # Processing device (auto/cpu/cuda/mps)
--batch-size INT          # Processing batch size (default: auto)
--workers INT             # Number of worker threads (default: auto)
--enable-gpu              # Force GPU acceleration
--disable-gpu             # Force CPU processing
--advanced-matching       # Enable advanced matching algorithms
--chunk-size INT          # Frames per processing chunk (default: 200)
```

#### **Model Options**
```bash
--clip-model NAME         # CLIP model to use (default: openai/clip-vit-base-patch32)
--yolo-model NAME         # YOLO model for object detection
--model-cache PATH        # Model cache directory
```

#### **Quality Options**
```bash
--quality FLOAT           # Compression quality 0.1-1.0 (default: 0.8)
--extract-objects         # Extract only detected objects
--confidence FLOAT        # Object detection confidence (default: 0.5)
```

### **Video Search Examples**

#### **Basic Examples**
```bash
# Simple search
python main.py --video movie.mp4 --query "car"

# Search with custom threshold
python main.py --video video.mp4 --query "person walking" --threshold 0.25

# High-quality search with clips
python main.py --video video.mp4 --query "red car" --create-clips --quality 0.9
```

#### **Performance Optimization**
```bash
# Fast processing (lower quality)
python main.py --video video.mp4 --query "person" \
  --frame-interval 60 --resolution 512x384 --disable-gpu

# High accuracy (slower)
python main.py --video video.mp4 --query "car" \
  --frame-interval 15 --advanced-matching --enable-gpu

# Large video processing
python main.py --video large_video.mp4 --query "object" \
  --chunk-size 100 --workers 4 --batch-size 16
```

#### **Advanced Examples**
```bash
# Custom output location
python main.py --video video.mp4 --query "sunset" \
  --output-dir /path/to/results --create-clips --clip-duration 5.0

# Specific model usage
python main.py --video video.mp4 --query "person" \
  --clip-model "openai/clip-vit-large-patch14" --device cuda

# Object extraction only
python main.py --video video.mp4 --query "dog" \
  --extract-objects --confidence 0.7 --create-thumbnails
```

## 🌐 Web Interface Mode

### **Basic Web Interface**
```bash
python main.py --web
```

### **Web Interface Options**
```bash
--port INT                # Port number (default: 8501)
--host HOST               # Host address (default: localhost)
--server-headless         # Run in headless mode
--server-address ADDRESS  # Server address
--browser-gather-usage-stats BOOL  # Gather usage statistics
```

### **Web Interface Examples**
```bash
# Default web interface
python main.py --web

# Custom port
python main.py --web --port 8502

# External access
python main.py --web --host 0.0.0.0 --port 8501

# Headless mode (no browser auto-open)
python main.py --web --server-headless
```

## 📹 Live Detection Mode

### **Basic Live Detection**
```bash
python main.py --live --query "SEARCH_QUERY"
```

### **Live Detection Options**

#### **Source Options**
```bash
--camera INT              # Camera index (default: 0)
--stream URL              # RTSP/HTTP stream URL
--input-source PATH       # Video file as live source
```

#### **Detection Parameters**
```bash
--detection-interval FLOAT  # Detection interval in seconds (default: 0.5)
--max-detections INT       # Maximum detections to keep (default: 20)
--confidence FLOAT         # Detection confidence threshold (default: 0.5)
--save-detections          # Auto-save detected images
```

#### **Processing Options**
```bash
--resolution WIDTHxHEIGHT  # Camera/stream resolution
--fps INT                 # Target frame rate
--buffer-size INT         # Stream buffer size
--reconnect               # Auto-reconnect on stream loss
```

### **Live Detection Examples**

#### **Camera Detection**
```bash
# Default camera
python main.py --live --query "person"

# Specific camera
python main.py --live --query "car" --camera 1

# High-frequency detection
python main.py --live --query "motion" --detection-interval 0.1
```

#### **Stream Detection**
```bash
# RTSP stream
python main.py --live --query "vehicle" \
  --stream "rtsp://192.168.1.100:554/stream"

# HTTP stream with authentication
python main.py --live --query "person" \
  --stream "******************************/mjpeg"

# YouTube live stream
python main.py --live --query "event" \
  --stream "https://youtube.com/watch?v=LIVE_ID"
```

#### **Advanced Live Detection**
```bash
# High-confidence detection with saving
python main.py --live --query "intruder" \
  --confidence 0.8 --save-detections --max-detections 50

# Custom resolution and frame rate
python main.py --live --query "object" \
  --resolution 1280x720 --fps 30 --detection-interval 1.0
```

## 📊 Batch Processing Mode

### **Batch Video Processing**
```bash
python main.py --batch-videos VIDEO1 VIDEO2 ... --query "SEARCH_QUERY"
```

### **Batch Query Processing**
```bash
python main.py --video VIDEO_PATH --batch-queries "QUERY1" "QUERY2" ...
```

### **Batch Processing Options**
```bash
--parallel-jobs INT       # Number of parallel processing jobs
--job-timeout INT         # Timeout per job in seconds
--continue-on-error       # Continue processing if one job fails
--output-format FORMAT    # Output format (json/csv/html)
```

### **Batch Processing Examples**

#### **Multiple Videos, Single Query**
```bash
# Process multiple videos
python main.py --batch-videos video1.mp4 video2.mp4 video3.mp4 \
  --query "red car" --parallel-jobs 2

# With custom settings
python main.py --batch-videos *.mp4 --query "person" \
  --threshold 0.25 --create-clips --parallel-jobs 4
```

#### **Single Video, Multiple Queries**
```bash
# Multiple queries on one video
python main.py --video movie.mp4 \
  --batch-queries "car" "person" "building" "outdoor scene"

# With different thresholds
python main.py --video video.mp4 \
  --batch-queries "person:0.3" "car:0.25" "background:0.15"
```

## 🔧 Configuration and Debugging

### **Configuration Options**
```bash
--config PATH             # Configuration file path
--save-config             # Save current settings to config file
--reset-config            # Reset to default configuration
--list-models             # List available AI models
--download-model NAME     # Download specific model
```

### **Debugging and Logging**
```bash
--verbose                 # Enable verbose output
--debug                   # Enable debug mode
--log-level LEVEL         # Set log level (DEBUG/INFO/WARNING/ERROR)
--log-file PATH           # Log file path
--profile                 # Enable performance profiling
--benchmark               # Run performance benchmarks
```

### **System Information**
```bash
--system-info             # Show system information
--gpu-info                # Show GPU information
--model-info              # Show loaded model information
--memory-info             # Show memory usage information
```

## 📈 Output and Reporting

### **Output Formats**
```bash
--output-format FORMAT    # json/csv/html/xml
--export-metadata         # Export detailed metadata
--create-report           # Generate comprehensive report
--report-format FORMAT    # Report format (pdf/html/markdown)
```

### **Progress and Status**
```bash
--progress                # Show progress bar
--quiet                   # Suppress output
--status-interval INT     # Status update interval in seconds
--no-color                # Disable colored output
```

## 🎯 Advanced Usage Patterns

### **Pipeline Processing**
```bash
# Process and pipe results
python main.py --video video.mp4 --query "car" --output-format json | \
  jq '.matches[] | select(.similarity_score > 0.3)'

# Chain multiple searches
for video in *.mp4; do
  python main.py --video "$video" --query "person" --create-clips
done
```

### **Automation Scripts**
```bash
#!/bin/bash
# Automated video analysis script

VIDEOS_DIR="/path/to/videos"
QUERIES=("person" "car" "building" "outdoor scene")

for video in "$VIDEOS_DIR"/*.mp4; do
  for query in "${QUERIES[@]}"; do
    python main.py --video "$video" --query "$query" \
      --output-dir "results/$(basename "$video" .mp4)/$query" \
      --create-thumbnails --output-format json
  done
done
```

### **Performance Monitoring**
```bash
# Monitor resource usage during processing
python main.py --video large_video.mp4 --query "object" \
  --profile --memory-info --verbose \
  2>&1 | tee processing.log
```

## 🐛 Error Handling and Troubleshooting

### **Common Error Codes**
- **Exit Code 0:** Success
- **Exit Code 1:** General error
- **Exit Code 2:** Invalid arguments
- **Exit Code 3:** File not found
- **Exit Code 4:** Processing error
- **Exit Code 5:** Model loading error

### **Debugging Commands**
```bash
# Test installation
python main.py --system-info --gpu-info

# Validate video file
python main.py --video video.mp4 --query "test" --debug

# Check model availability
python main.py --list-models

# Memory usage analysis
python main.py --video video.mp4 --query "object" --memory-info --profile
```

### **Recovery Options**
```bash
# Reset configuration
python main.py --reset-config

# Clear model cache
python main.py --clear-cache

# Force CPU processing
python main.py --video video.mp4 --query "object" --device cpu

# Minimal processing
python main.py --video video.mp4 --query "object" \
  --frame-interval 120 --resolution 256x192 --batch-size 1
```

## 📚 Integration Examples

### **Shell Integration**
```bash
# Create alias for common usage
alias video-search='python /path/to/ai-video-search/main.py'

# Use in shell scripts
video-search --video "$1" --query "$2" --create-clips
```

### **Cron Job Integration**
```bash
# Automated processing every hour
0 * * * * cd /path/to/ai-video-search && python main.py --batch-videos /incoming/*.mp4 --query "security" --save-detections
```

### **Docker Integration**
```bash
# Run in Docker container
docker run -v /videos:/app/videos ai-video-search \
  python main.py --video /app/videos/video.mp4 --query "person"
```

---

**Master the CLI:** This comprehensive reference covers all command line options and usage patterns. Use it as a quick reference while developing your video analysis workflows!
