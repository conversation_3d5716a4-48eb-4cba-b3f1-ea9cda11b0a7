#!/usr/bin/env python3
"""
Professional installation script for AI-Powered Video Content Search.
Handles dependency installation, system checks, and initial setup.
"""

import os
import sys
import subprocess
import platform
import shutil
import json
from pathlib import Path
from typing import List, Dict, Tuple, Optional


class Colors:
    """ANSI color codes for terminal output."""
    HEADER = '\033[95m'
    OKBLUE = '\033[94m'
    OKCYAN = '\033[96m'
    OKGREEN = '\033[92m'
    WARNING = '\033[93m'
    FAIL = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'


class InstallationManager:
    """Manages the installation process for the AI Video Search application."""
    
    def __init__(self):
        self.python_version = sys.version_info
        self.platform = platform.system()
        self.architecture = platform.machine()
        self.errors = []
        self.warnings = []
        
    def print_header(self):
        """Print installation header."""
        print(f"{Colors.HEADER}{Colors.BOLD}")
        print("=" * 70)
        print("🎬 AI-Powered Video Content Search - Installation")
        print("=" * 70)
        print(f"{Colors.ENDC}")
        print(f"Platform: {self.platform} {self.architecture}")
        print(f"Python: {self.python_version.major}.{self.python_version.minor}.{self.python_version.micro}")
        print()
    
    def check_python_version(self) -> bool:
        """Check if Python version is compatible."""
        print("🐍 Checking Python version...")
        
        if self.python_version < (3, 8):
            self.errors.append(f"Python 3.8+ required, found {self.python_version.major}.{self.python_version.minor}")
            print(f"{Colors.FAIL}❌ Python 3.8+ required, found {self.python_version.major}.{self.python_version.minor}{Colors.ENDC}")
            return False
        
        print(f"{Colors.OKGREEN}✅ Python {self.python_version.major}.{self.python_version.minor}.{self.python_version.micro} is compatible{Colors.ENDC}")
        return True
    
    def check_system_dependencies(self) -> bool:
        """Check for required system dependencies."""
        print("\n🔧 Checking system dependencies...")
        
        dependencies = {
            'git': 'Git version control',
            'ffmpeg': 'FFmpeg for video processing'
        }
        
        missing = []
        for cmd, description in dependencies.items():
            if not shutil.which(cmd):
                missing.append((cmd, description))
                print(f"{Colors.WARNING}⚠️  {cmd} not found ({description}){Colors.ENDC}")
            else:
                print(f"{Colors.OKGREEN}✅ {cmd} found{Colors.ENDC}")
        
        if missing:
            print(f"\n{Colors.WARNING}📋 Missing dependencies:{Colors.ENDC}")
            for cmd, desc in missing:
                print(f"   - {cmd}: {desc}")
            
            if self.platform == "Windows":
                print(f"\n{Colors.OKCYAN}💡 Windows installation tips:{Colors.ENDC}")
                print("   - Install Git: https://git-scm.com/download/win")
                print("   - Install FFmpeg: https://ffmpeg.org/download.html")
                print("   - Or use chocolatey: choco install git ffmpeg")
            elif self.platform == "Darwin":  # macOS
                print(f"\n{Colors.OKCYAN}💡 macOS installation tips:{Colors.ENDC}")
                print("   - Install Homebrew: https://brew.sh/")
                print("   - Run: brew install git ffmpeg")
            else:  # Linux
                print(f"\n{Colors.OKCYAN}💡 Linux installation tips:{Colors.ENDC}")
                print("   - Ubuntu/Debian: sudo apt install git ffmpeg")
                print("   - CentOS/RHEL: sudo yum install git ffmpeg")
                print("   - Arch: sudo pacman -S git ffmpeg")
            
            self.warnings.extend([f"Missing {cmd}" for cmd, _ in missing])
        
        return len(missing) == 0
    
    def create_directories(self) -> bool:
        """Create necessary directories."""
        print("\n📁 Creating directories...")
        
        directories = [
            "static",
            "static/output_clips",
            "temp_videos",
            "test_output",
            "models/cache",
            "logs",
            "data",
            "docs"
        ]
        
        try:
            for directory in directories:
                Path(directory).mkdir(parents=True, exist_ok=True)
                print(f"{Colors.OKGREEN}   ✅ {directory}{Colors.ENDC}")
            return True
        except Exception as e:
            self.errors.append(f"Failed to create directories: {e}")
            print(f"{Colors.FAIL}❌ Error creating directories: {e}{Colors.ENDC}")
            return False
    
    def install_python_dependencies(self) -> bool:
        """Install Python dependencies."""
        print("\n📦 Installing Python dependencies...")
        
        try:
            # Upgrade pip first
            print("   Upgrading pip...")
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", "--upgrade", "pip"
            ], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            
            # Install requirements
            print("   Installing requirements...")
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
            ])
            
            print(f"{Colors.OKGREEN}✅ All Python dependencies installed{Colors.ENDC}")
            return True
            
        except subprocess.CalledProcessError as e:
            self.errors.append(f"Failed to install Python dependencies: {e}")
            print(f"{Colors.FAIL}❌ Error installing dependencies: {e}{Colors.ENDC}")
            return False
    
    def check_gpu_support(self) -> Dict[str, bool]:
        """Check for GPU support."""
        print("\n🎮 Checking GPU support...")
        
        gpu_info = {
            'cuda_available': False,
            'mps_available': False,
            'device_count': 0,
            'device_name': None
        }
        
        try:
            import torch
            
            # Check CUDA
            if torch.cuda.is_available():
                gpu_info['cuda_available'] = True
                gpu_info['device_count'] = torch.cuda.device_count()
                gpu_info['device_name'] = torch.cuda.get_device_name(0)
                print(f"{Colors.OKGREEN}✅ CUDA available: {gpu_info['device_name']}{Colors.ENDC}")
            
            # Check MPS (Apple Silicon)
            if hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
                gpu_info['mps_available'] = True
                print(f"{Colors.OKGREEN}✅ MPS (Apple Silicon) available{Colors.ENDC}")
            
            if not gpu_info['cuda_available'] and not gpu_info['mps_available']:
                print(f"{Colors.WARNING}⚠️  No GPU acceleration available, will use CPU{Colors.ENDC}")
                
        except ImportError:
            print(f"{Colors.WARNING}⚠️  PyTorch not installed yet, GPU check will be done after installation{Colors.ENDC}")
        
        return gpu_info
    
    def test_installation(self) -> bool:
        """Test the installation by importing key modules."""
        print("\n🧪 Testing installation...")
        
        test_imports = [
            ('torch', 'PyTorch'),
            ('torchvision', 'TorchVision'),
            ('transformers', 'Transformers'),
            ('cv2', 'OpenCV'),
            ('streamlit', 'Streamlit'),
            ('PIL', 'Pillow'),
            ('moviepy.editor', 'MoviePy'),
            ('ultralytics', 'Ultralytics'),
            ('numpy', 'NumPy'),
            ('scipy', 'SciPy'),
            ('matplotlib', 'Matplotlib')
        ]
        
        failed_imports = []
        
        for module, name in test_imports:
            try:
                __import__(module)
                print(f"{Colors.OKGREEN}   ✅ {name}{Colors.ENDC}")
            except ImportError as e:
                failed_imports.append((module, name, str(e)))
                print(f"{Colors.FAIL}   ❌ {name}: {e}{Colors.ENDC}")
        
        if failed_imports:
            self.errors.extend([f"Failed to import {name}" for _, name, _ in failed_imports])
            return False
        
        print(f"{Colors.OKGREEN}✅ All imports successful{Colors.ENDC}")
        return True
    
    def create_config_file(self) -> bool:
        """Create initial configuration file."""
        print("\n⚙️  Creating configuration file...")
        
        try:
            from config_advanced import AdvancedConfig
            config = AdvancedConfig()
            config.save_config()
            print(f"{Colors.OKGREEN}✅ Configuration file created: config.json{Colors.ENDC}")
            return True
        except Exception as e:
            self.errors.append(f"Failed to create config file: {e}")
            print(f"{Colors.FAIL}❌ Error creating config file: {e}{Colors.ENDC}")
            return False
    
    def create_launcher_scripts(self) -> bool:
        """Create launcher scripts for different platforms."""
        print("\n🚀 Creating launcher scripts...")
        
        try:
            # Windows batch file
            if self.platform == "Windows":
                with open("launch.bat", "w") as f:
                    f.write("@echo off\n")
                    f.write("echo Starting AI Video Search...\n")
                    f.write("python main.py --web\n")
                    f.write("pause\n")
                print(f"{Colors.OKGREEN}   ✅ launch.bat{Colors.ENDC}")
            
            # Unix shell script
            with open("launch.sh", "w") as f:
                f.write("#!/bin/bash\n")
                f.write("echo 'Starting AI Video Search...'\n")
                f.write("python main.py --web\n")
            
            # Make shell script executable
            if self.platform != "Windows":
                os.chmod("launch.sh", 0o755)
            
            print(f"{Colors.OKGREEN}   ✅ launch.sh{Colors.ENDC}")
            return True
            
        except Exception as e:
            self.errors.append(f"Failed to create launcher scripts: {e}")
            print(f"{Colors.FAIL}❌ Error creating launcher scripts: {e}{Colors.ENDC}")
            return False
    
    def print_summary(self, success: bool):
        """Print installation summary."""
        print(f"\n{Colors.HEADER}{Colors.BOLD}")
        print("=" * 70)
        print("📋 Installation Summary")
        print("=" * 70)
        print(f"{Colors.ENDC}")
        
        if success:
            print(f"{Colors.OKGREEN}{Colors.BOLD}🎉 Installation completed successfully!{Colors.ENDC}")
        else:
            print(f"{Colors.FAIL}{Colors.BOLD}❌ Installation failed!{Colors.ENDC}")
        
        if self.warnings:
            print(f"\n{Colors.WARNING}⚠️  Warnings:{Colors.ENDC}")
            for warning in self.warnings:
                print(f"   - {warning}")
        
        if self.errors:
            print(f"\n{Colors.FAIL}❌ Errors:{Colors.ENDC}")
            for error in self.errors:
                print(f"   - {error}")
        
        if success:
            print(f"\n{Colors.OKCYAN}🚀 Next Steps:{Colors.ENDC}")
            print("   1. Test the installation:")
            print("      python test_application.py")
            print("   2. Start the web interface:")
            print("      python main.py --web")
            print("      or use: ./launch.sh (Unix) / launch.bat (Windows)")
            print("   3. Try command line search:")
            print("      python main.py --video video.mp4 --query 'red cap'")
            print("   4. Read the documentation:")
            print("      README.md and docs/ folder")
        
        print(f"\n{Colors.HEADER}=" * 70 + f"{Colors.ENDC}")
    
    def run_installation(self) -> bool:
        """Run the complete installation process."""
        self.print_header()
        
        steps = [
            ("Python Version", self.check_python_version),
            ("System Dependencies", self.check_system_dependencies),
            ("Create Directories", self.create_directories),
            ("Install Python Dependencies", self.install_python_dependencies),
            ("Test Installation", self.test_installation),
            ("Create Config File", self.create_config_file),
            ("Create Launcher Scripts", self.create_launcher_scripts)
        ]
        
        success = True
        for step_name, step_func in steps:
            if not step_func():
                success = False
                if step_name in ["Python Version", "Install Python Dependencies"]:
                    # Critical failures
                    break
        
        # Check GPU support (non-critical)
        self.check_gpu_support()
        
        self.print_summary(success)
        return success


def main():
    """Main installation function."""
    installer = InstallationManager()
    success = installer.run_installation()
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
