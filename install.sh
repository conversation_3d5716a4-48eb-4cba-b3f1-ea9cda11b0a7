#!/bin/bash
# AI-Powered Video Content Search - Unix/Linux Installation Script
# This script automates the installation process on Unix-like systems

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_header() {
    echo -e "${PURPLE}========================================================================"
    echo -e " AI-Powered Video Content Search - Unix/Linux Installation"
    echo -e "========================================================================${NC}"
    echo
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Main installation function
main() {
    print_header
    
    # Check Python installation
    echo "🐍 Checking Python installation..."
    if ! command_exists python3; then
        if ! command_exists python; then
            print_error "Python is not installed"
            echo "Please install Python 3.8+ using your system package manager:"
            echo "  Ubuntu/Debian: sudo apt install python3 python3-pip"
            echo "  CentOS/RHEL: sudo yum install python3 python3-pip"
            echo "  macOS: brew install python3"
            exit 1
        else
            PYTHON_CMD="python"
        fi
    else
        PYTHON_CMD="python3"
    fi
    
    # Check Python version
    PYTHON_VERSION=$($PYTHON_CMD --version 2>&1 | cut -d' ' -f2)
    print_success "Found Python $PYTHON_VERSION"
    
    # Check if version is 3.8+
    PYTHON_MAJOR=$(echo $PYTHON_VERSION | cut -d'.' -f1)
    PYTHON_MINOR=$(echo $PYTHON_VERSION | cut -d'.' -f2)
    
    if [ "$PYTHON_MAJOR" -lt 3 ] || ([ "$PYTHON_MAJOR" -eq 3 ] && [ "$PYTHON_MINOR" -lt 8 ]); then
        print_error "Python 3.8+ is required, found $PYTHON_VERSION"
        exit 1
    fi
    
    # Check pip
    echo
    echo "📦 Checking pip installation..."
    if ! command_exists pip3 && ! command_exists pip; then
        print_error "pip is not installed"
        echo "Please install pip using your system package manager"
        exit 1
    fi
    
    if command_exists pip3; then
        PIP_CMD="pip3"
    else
        PIP_CMD="pip"
    fi
    
    print_success "Found pip"
    
    # Check system dependencies
    echo
    echo "🔧 Checking system dependencies..."
    
    MISSING_DEPS=()
    
    if ! command_exists git; then
        MISSING_DEPS+=("git")
    else
        print_success "git found"
    fi
    
    if ! command_exists ffmpeg; then
        MISSING_DEPS+=("ffmpeg")
    else
        print_success "ffmpeg found"
    fi
    
    if [ ${#MISSING_DEPS[@]} -ne 0 ]; then
        print_warning "Missing dependencies: ${MISSING_DEPS[*]}"
        echo "Install them using your system package manager:"
        echo "  Ubuntu/Debian: sudo apt install ${MISSING_DEPS[*]}"
        echo "  CentOS/RHEL: sudo yum install ${MISSING_DEPS[*]}"
        echo "  macOS: brew install ${MISSING_DEPS[*]}"
        echo
        read -p "Continue anyway? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
    
    # Create virtual environment (optional but recommended)
    echo
    echo "🏗️  Setting up Python environment..."
    if [ ! -d "venv" ]; then
        print_info "Creating virtual environment..."
        $PYTHON_CMD -m venv venv
        print_success "Virtual environment created"
    else
        print_info "Virtual environment already exists"
    fi
    
    # Activate virtual environment
    source venv/bin/activate
    print_success "Virtual environment activated"
    
    # Upgrade pip
    echo
    echo "⬆️  Upgrading pip..."
    pip install --upgrade pip
    
    # Install Python dependencies
    echo
    echo "📦 Installing Python dependencies..."
    echo "This may take several minutes..."
    
    if [ -f "requirements.txt" ]; then
        pip install -r requirements.txt
        print_success "Python dependencies installed"
    else
        print_error "requirements.txt not found"
        exit 1
    fi
    
    # Create directories
    echo
    echo "📁 Creating directories..."
    mkdir -p static/output_clips
    mkdir -p temp_videos
    mkdir -p test_output
    mkdir -p models/cache
    mkdir -p logs
    print_success "Directories created"
    
    # Run installation script
    echo
    echo "🔧 Running installation script..."
    if [ -f "install.py" ]; then
        python install.py
    else
        print_warning "install.py not found, skipping"
    fi
    
    # Create launcher scripts
    echo
    echo "🚀 Creating launcher scripts..."
    
    # Web interface launcher
    cat > launch_web.sh << 'EOF'
#!/bin/bash
echo "Starting AI Video Search Web Interface..."
source venv/bin/activate 2>/dev/null || true
python main.py --web
EOF
    chmod +x launch_web.sh
    
    # Live detection launcher
    cat > launch_live.sh << 'EOF'
#!/bin/bash
echo "Starting AI Video Search Live Detection..."
read -p "Enter search query (e.g., 'person', 'car'): " query
source venv/bin/activate 2>/dev/null || true
python main.py --live --query "$query"
EOF
    chmod +x launch_live.sh
    
    # Command line example
    cat > example_search.sh << 'EOF'
#!/bin/bash
# Example command line search
# Usage: ./example_search.sh video.mp4 "search query"
if [ $# -ne 2 ]; then
    echo "Usage: $0 <video_file> <search_query>"
    echo "Example: $0 video.mp4 'red car'"
    exit 1
fi
source venv/bin/activate 2>/dev/null || true
python main.py --video "$1" --query "$2"
EOF
    chmod +x example_search.sh
    
    print_success "Launcher scripts created"
    
    # Run tests
    echo
    echo "🧪 Running application tests..."
    if [ -f "test_application.py" ]; then
        python test_application.py || print_warning "Some tests failed"
    else
        print_warning "test_application.py not found, skipping tests"
    fi
    
    # Installation complete
    echo
    print_header
    print_success "Installation Complete!"
    echo
    echo -e "${CYAN}Next steps:${NC}"
    echo "  1. Test the installation: python test_application.py"
    echo "  2. Start web interface: ./launch_web.sh (or python main.py --web)"
    echo "  3. Try live detection: ./launch_live.sh"
    echo "  4. Command line usage: ./example_search.sh video.mp4 'red car'"
    echo
    echo -e "${CYAN}Virtual Environment:${NC}"
    echo "  Activate: source venv/bin/activate"
    echo "  Deactivate: deactivate"
    echo
    echo "For help and documentation, see README.md"
    echo
}

# Handle interruption
trap 'echo -e "\n${YELLOW}Installation interrupted${NC}"; exit 1' INT

# Run main function
main "$@"
