"""
Video frame extraction utilities for AI-powered video search.
"""

import cv2
import numpy as np
from typing import List, Tuple, Optional
import os
from tqdm import tqdm


class FrameExtractor:
    """Extract frames from video files at specified intervals."""

    def __init__(self, every_n_frames: int = 30, max_frames: Optional[int] = None,
                 target_resolution: Optional[Tuple[int, int]] = None, quality_factor: float = 0.7):
        """
        Initialize frame extractor.

        Args:
            every_n_frames: Extract every N frames (default: 30, ~1 frame per second for 30fps video)
            max_frames: Maximum number of frames to extract (None for no limit)
            target_resolution: Resize frames to this resolution (width, height) for memory efficiency
            quality_factor: JPEG compression quality for memory optimization (0.1-1.0)
        """
        self.every_n_frames = every_n_frames
        self.max_frames = max_frames
        self.target_resolution = target_resolution
        self.quality_factor = quality_factor
    
    def extract_frames(self, video_path: str) -> List[Tuple[int, np.ndarray, float]]:
        """
        Extract frames from video file.
        
        Args:
            video_path: Path to the video file
            
        Returns:
            List of tuples containing (frame_index, frame_array, timestamp_seconds)
        """
        if not os.path.exists(video_path):
            raise FileNotFoundError(f"Video file not found: {video_path}")
        
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            raise ValueError(f"Could not open video file: {video_path}")
        
        # Get video properties
        fps = cap.get(cv2.CAP_PROP_FPS)
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        
        frames = []
        frame_idx = 0
        extracted_count = 0
        
        print(f"Extracting frames from {video_path}")
        print(f"Video FPS: {fps}, Total frames: {total_frames}")
        
        # Calculate expected number of frames to extract
        expected_frames = min(
            total_frames // self.every_n_frames,
            self.max_frames if self.max_frames else float('inf')
        )
        
        with tqdm(total=int(expected_frames), desc="Extracting frames") as pbar:
            while cap.isOpened():
                ret, frame = cap.read()
                if not ret:
                    break
                
                if frame_idx % self.every_n_frames == 0:
                    # Convert BGR to RGB for consistency with PIL/CLIP
                    frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)

                    # Resize frame if target resolution is specified
                    if self.target_resolution:
                        frame_rgb = cv2.resize(frame_rgb, self.target_resolution)

                    # Compress frame for memory efficiency with large videos
                    if self.quality_factor < 1.0:
                        frame_rgb = self._compress_frame(frame_rgb)

                    timestamp = frame_idx / fps
                    frames.append((frame_idx, frame_rgb, timestamp))
                    extracted_count += 1
                    pbar.update(1)

                    if self.max_frames and extracted_count >= self.max_frames:
                        break
                
                frame_idx += 1
        
        cap.release()
        print(f"Extracted {len(frames)} frames from video")

        # Memory usage info
        if frames:
            frame_size = frames[0][1].nbytes if len(frames) > 0 else 0
            total_memory_mb = (len(frames) * frame_size) / (1024 * 1024)
            print(f"Estimated memory usage: {total_memory_mb:.1f} MB")

        return frames
    
    def extract_frames_by_time(self, video_path: str, interval_seconds: float = 1.0) -> List[Tuple[int, np.ndarray, float]]:
        """
        Extract frames at specific time intervals.
        
        Args:
            video_path: Path to the video file
            interval_seconds: Time interval between extracted frames in seconds
            
        Returns:
            List of tuples containing (frame_index, frame_array, timestamp_seconds)
        """
        if not os.path.exists(video_path):
            raise FileNotFoundError(f"Video file not found: {video_path}")
        
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            raise ValueError(f"Could not open video file: {video_path}")
        
        fps = cap.get(cv2.CAP_PROP_FPS)
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        duration = total_frames / fps
        
        frames = []
        current_time = 0.0
        extracted_count = 0
        
        print(f"Extracting frames every {interval_seconds} seconds")
        print(f"Video duration: {duration:.2f} seconds")
        
        expected_frames = int(duration / interval_seconds)
        if self.max_frames:
            expected_frames = min(expected_frames, self.max_frames)
        
        with tqdm(total=expected_frames, desc="Extracting frames by time") as pbar:
            while current_time < duration:
                # Set video position to current time
                cap.set(cv2.CAP_PROP_POS_MSEC, current_time * 1000)
                ret, frame = cap.read()
                
                if not ret:
                    break
                
                # Get actual frame index
                frame_idx = int(cap.get(cv2.CAP_PROP_POS_FRAMES))
                
                # Convert BGR to RGB
                frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                frames.append((frame_idx, frame_rgb, current_time))
                extracted_count += 1
                pbar.update(1)
                
                if self.max_frames and extracted_count >= self.max_frames:
                    break
                
                current_time += interval_seconds
        
        cap.release()
        print(f"Extracted {len(frames)} frames from video")
        return frames
    
    def get_video_info(self, video_path: str) -> dict:
        """
        Get basic information about the video file.
        
        Args:
            video_path: Path to the video file
            
        Returns:
            Dictionary containing video information
        """
        if not os.path.exists(video_path):
            raise FileNotFoundError(f"Video file not found: {video_path}")
        
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            raise ValueError(f"Could not open video file: {video_path}")
        
        info = {
            'fps': cap.get(cv2.CAP_PROP_FPS),
            'total_frames': int(cap.get(cv2.CAP_PROP_FRAME_COUNT)),
            'width': int(cap.get(cv2.CAP_PROP_FRAME_WIDTH)),
            'height': int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT)),
            'duration_seconds': cap.get(cv2.CAP_PROP_FRAME_COUNT) / cap.get(cv2.CAP_PROP_FPS)
        }
        
        cap.release()
        return info

    def _compress_frame(self, frame: np.ndarray) -> np.ndarray:
        """
        Compress frame using JPEG compression to reduce memory usage.

        Args:
            frame: Input frame array

        Returns:
            Compressed frame array
        """
        try:
            from PIL import Image
            import io

            # Convert to PIL Image
            pil_image = Image.fromarray(frame)

            # Compress using JPEG
            buffer = io.BytesIO()
            quality = int(self.quality_factor * 100)
            pil_image.save(buffer, format='JPEG', quality=quality, optimize=True)
            buffer.seek(0)

            # Load back as array
            compressed_image = Image.open(buffer)
            return np.array(compressed_image)

        except Exception:
            # Fallback to original frame if compression fails
            return frame

    def extract_frames_chunked(self, video_path: str, chunk_size: int = 100) -> List[Tuple[int, np.ndarray, float]]:
        """
        Extract frames in chunks to handle very large videos with limited memory.

        Args:
            video_path: Path to the video file
            chunk_size: Number of frames to process at once

        Returns:
            List of tuples containing (frame_index, frame_array, timestamp)
        """
        if not os.path.exists(video_path):
            raise FileNotFoundError(f"Video file not found: {video_path}")

        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            raise ValueError(f"Could not open video file: {video_path}")

        fps = cap.get(cv2.CAP_PROP_FPS)
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))

        all_frames = []
        frame_idx = 0
        extracted_count = 0

        print(f"Processing large video in chunks of {chunk_size} frames")

        with tqdm(total=total_frames, desc="Processing video") as pbar:
            while cap.isOpened() and frame_idx < total_frames:
                chunk_frames = []

                # Process chunk
                for _ in range(chunk_size):
                    ret, frame = cap.read()
                    if not ret:
                        break

                    if frame_idx % self.every_n_frames == 0:
                        frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)

                        if self.target_resolution:
                            frame_rgb = cv2.resize(frame_rgb, self.target_resolution)

                        if self.quality_factor < 1.0:
                            frame_rgb = self._compress_frame(frame_rgb)

                        timestamp = frame_idx / fps
                        chunk_frames.append((frame_idx, frame_rgb, timestamp))
                        extracted_count += 1

                        if self.max_frames and extracted_count >= self.max_frames:
                            break

                    frame_idx += 1
                    pbar.update(1)

                all_frames.extend(chunk_frames)

                if self.max_frames and extracted_count >= self.max_frames:
                    break

        cap.release()
        print(f"Extracted {len(all_frames)} frames from large video")
        return all_frames


def extract_frames_simple(video_path: str, every_n_frames: int = 30) -> List[Tuple[int, np.ndarray]]:
    """
    Simple function to extract frames from video.
    
    Args:
        video_path: Path to the video file
        every_n_frames: Extract every N frames
        
    Returns:
        List of tuples containing (frame_index, frame_array)
    """
    extractor = FrameExtractor(every_n_frames=every_n_frames)
    frames_with_time = extractor.extract_frames(video_path)
    return [(frame_idx, frame) for frame_idx, frame, _ in frames_with_time]
