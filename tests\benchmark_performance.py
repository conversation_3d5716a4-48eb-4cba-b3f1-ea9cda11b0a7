#!/usr/bin/env python3
"""
Performance benchmarking script for AI-Powered Video Content Search.
Measures processing times, memory usage, and accuracy metrics.
"""

import os
import sys
import time
import psutil
import numpy as np
import cv2
import tempfile
import json
from pathlib import Path
from typing import Dict, List, Any
import matplotlib.pyplot as plt
import pandas as pd

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.clip_match import VideoSearchEngine
from utils.frame_extraction import FrameExtractor
from utils.live_detection import LiveVideoDetector
from config_advanced import AdvancedConfig


class PerformanceBenchmark:
    """Comprehensive performance benchmarking suite."""
    
    def __init__(self, output_dir: str = "benchmark_results"):
        """Initialize benchmark suite."""
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        self.results = {
            'video_search': [],
            'frame_extraction': [],
            'live_detection': [],
            'memory_usage': [],
            'system_info': self._get_system_info()
        }
        
        print("🚀 Performance Benchmark Suite Initialized")
        print(f"📊 Results will be saved to: {self.output_dir}")
    
    def _get_system_info(self) -> Dict[str, Any]:
        """Get system information for benchmark context."""
        import platform
        import torch
        
        return {
            'platform': platform.platform(),
            'processor': platform.processor(),
            'python_version': platform.python_version(),
            'cpu_count': os.cpu_count(),
            'memory_total_gb': psutil.virtual_memory().total / (1024**3),
            'torch_version': torch.__version__,
            'cuda_available': torch.cuda.is_available(),
            'cuda_device_count': torch.cuda.device_count() if torch.cuda.is_available() else 0,
            'cuda_device_name': torch.cuda.get_device_name(0) if torch.cuda.is_available() else None
        }
    
    def create_test_videos(self, num_videos: int = 5) -> List[str]:
        """Create test videos of different sizes and complexities."""
        test_videos = []
        
        print(f"📹 Creating {num_videos} test videos...")
        
        for i in range(num_videos):
            # Create videos of increasing complexity
            duration = 5 + i * 5  # 5, 10, 15, 20, 25 seconds
            fps = 30
            width = 640 + i * 320  # 640, 960, 1280, 1600, 1920
            height = 480 + i * 240  # 480, 720, 960, 1200, 1440
            
            video_path = self.output_dir / f"test_video_{i+1}_{width}x{height}_{duration}s.mp4"
            
            # Create video
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            out = cv2.VideoWriter(str(video_path), fourcc, fps, (width, height))
            
            total_frames = duration * fps
            for frame_num in range(total_frames):
                # Create frame with varying content
                frame = self._create_test_frame(frame_num, width, height, total_frames)
                out.write(frame)
            
            out.release()
            test_videos.append(str(video_path))
            
            print(f"   ✅ Created: {video_path.name} ({width}x{height}, {duration}s)")
        
        return test_videos
    
    def _create_test_frame(self, frame_num: int, width: int, height: int, total_frames: int) -> np.ndarray:
        """Create a test frame with varying content."""
        # Create base frame
        frame = np.zeros((height, width, 3), dtype=np.uint8)
        
        # Add moving objects
        progress = frame_num / total_frames
        
        # Moving red circle
        center_x = int(width * progress)
        center_y = height // 2
        cv2.circle(frame, (center_x, center_y), 50, (0, 0, 255), -1)
        
        # Moving blue rectangle
        rect_x = int(width * (1 - progress))
        rect_y = height // 4
        cv2.rectangle(frame, (rect_x, rect_y), (rect_x + 100, rect_y + 60), (255, 0, 0), -1)
        
        # Add noise for complexity
        noise = np.random.randint(0, 50, (height, width, 3), dtype=np.uint8)
        frame = cv2.add(frame, noise)
        
        return frame
    
    def benchmark_video_search(self, test_videos: List[str]) -> None:
        """Benchmark video search performance."""
        print("\n🔍 Benchmarking Video Search Performance...")
        
        queries = ["red circle", "blue rectangle", "moving object", "color", "shape"]
        
        for video_path in test_videos:
            video_name = Path(video_path).name
            print(f"   📹 Testing: {video_name}")
            
            # Test different configurations
            configs = [
                {"frame_interval": 30, "resolution": (512, 384), "chunked": False},
                {"frame_interval": 15, "resolution": (512, 384), "chunked": True},
                {"frame_interval": 30, "resolution": (256, 192), "chunked": True},
            ]
            
            for config in configs:
                for query in queries:
                    result = self._benchmark_single_search(video_path, query, config)
                    result['video_name'] = video_name
                    result['query'] = query
                    result['config'] = config
                    self.results['video_search'].append(result)
    
    def _benchmark_single_search(self, video_path: str, query: str, config: Dict) -> Dict[str, Any]:
        """Benchmark a single video search."""
        # Monitor memory before
        process = psutil.Process()
        memory_before = process.memory_info().rss / (1024**2)  # MB
        
        # Create search engine
        search_engine = VideoSearchEngine(
            frame_interval=config['frame_interval'],
            target_resolution=config['resolution'],
            use_chunked_processing=config['chunked'],
            enable_parallel_processing=True
        )
        
        # Perform search
        start_time = time.time()
        
        try:
            results = search_engine.search_video(
                video_path=video_path,
                query=query,
                similarity_threshold=0.2,
                top_k=10,
                create_clips=False,
                create_thumbnails=False
            )
            
            end_time = time.time()
            processing_time = end_time - start_time
            
            # Monitor memory after
            memory_after = process.memory_info().rss / (1024**2)  # MB
            memory_used = memory_after - memory_before
            
            # Get video info
            video_info = search_engine._get_video_info(video_path)
            
            return {
                'processing_time': processing_time,
                'memory_used_mb': memory_used,
                'matches_found': len(results.get('matches', [])),
                'frames_processed': results.get('stats', {}).get('total_frames_extracted', 0),
                'video_duration': video_info.get('duration_seconds', 0),
                'video_size_mb': os.path.getsize(video_path) / (1024**2),
                'fps': video_info.get('fps', 0),
                'resolution': f"{video_info.get('width', 0)}x{video_info.get('height', 0)}",
                'success': results.get('success', False),
                'error': results.get('error', None)
            }
            
        except Exception as e:
            return {
                'processing_time': time.time() - start_time,
                'memory_used_mb': 0,
                'matches_found': 0,
                'frames_processed': 0,
                'video_duration': 0,
                'video_size_mb': os.path.getsize(video_path) / (1024**2),
                'fps': 0,
                'resolution': "unknown",
                'success': False,
                'error': str(e)
            }
    
    def benchmark_frame_extraction(self, test_videos: List[str]) -> None:
        """Benchmark frame extraction performance."""
        print("\n🎞️ Benchmarking Frame Extraction Performance...")
        
        frame_intervals = [10, 30, 60]
        
        for video_path in test_videos:
            video_name = Path(video_path).name
            print(f"   📹 Testing: {video_name}")
            
            for interval in frame_intervals:
                result = self._benchmark_frame_extraction(video_path, interval)
                result['video_name'] = video_name
                result['frame_interval'] = interval
                self.results['frame_extraction'].append(result)
    
    def _benchmark_frame_extraction(self, video_path: str, frame_interval: int) -> Dict[str, Any]:
        """Benchmark frame extraction for a single video."""
        extractor = FrameExtractor(every_n_frames=frame_interval)
        
        # Monitor memory
        process = psutil.Process()
        memory_before = process.memory_info().rss / (1024**2)
        
        start_time = time.time()
        
        try:
            frames = extractor.extract_frames(video_path)
            video_info = extractor.get_video_info(video_path)
            
            end_time = time.time()
            processing_time = end_time - start_time
            
            memory_after = process.memory_info().rss / (1024**2)
            memory_used = memory_after - memory_before
            
            return {
                'processing_time': processing_time,
                'memory_used_mb': memory_used,
                'frames_extracted': len(frames),
                'video_duration': video_info.get('duration_seconds', 0),
                'video_size_mb': os.path.getsize(video_path) / (1024**2),
                'extraction_rate_fps': len(frames) / processing_time if processing_time > 0 else 0,
                'success': True,
                'error': None
            }
            
        except Exception as e:
            return {
                'processing_time': time.time() - start_time,
                'memory_used_mb': 0,
                'frames_extracted': 0,
                'video_duration': 0,
                'video_size_mb': os.path.getsize(video_path) / (1024**2),
                'extraction_rate_fps': 0,
                'success': False,
                'error': str(e)
            }
    
    def generate_report(self) -> None:
        """Generate comprehensive performance report."""
        print("\n📊 Generating Performance Report...")
        
        # Save raw results
        results_file = self.output_dir / "benchmark_results.json"
        with open(results_file, 'w') as f:
            json.dump(self.results, f, indent=2, default=str)
        
        # Generate summary statistics
        self._generate_summary_stats()
        
        # Generate visualizations
        self._generate_visualizations()
        
        print(f"✅ Report generated: {self.output_dir}")
    
    def _generate_summary_stats(self) -> None:
        """Generate summary statistics."""
        summary = {
            'system_info': self.results['system_info'],
            'video_search_summary': self._summarize_video_search(),
            'frame_extraction_summary': self._summarize_frame_extraction()
        }
        
        summary_file = self.output_dir / "summary_stats.json"
        with open(summary_file, 'w') as f:
            json.dump(summary, f, indent=2, default=str)
    
    def _summarize_video_search(self) -> Dict[str, Any]:
        """Summarize video search results."""
        if not self.results['video_search']:
            return {}
        
        df = pd.DataFrame(self.results['video_search'])
        successful = df[df['success'] == True]
        
        if len(successful) == 0:
            return {'error': 'No successful searches'}
        
        return {
            'total_searches': len(df),
            'successful_searches': len(successful),
            'success_rate': len(successful) / len(df),
            'avg_processing_time': successful['processing_time'].mean(),
            'avg_memory_usage': successful['memory_used_mb'].mean(),
            'avg_matches_found': successful['matches_found'].mean(),
            'processing_time_range': [successful['processing_time'].min(), successful['processing_time'].max()],
            'memory_usage_range': [successful['memory_used_mb'].min(), successful['memory_used_mb'].max()]
        }
    
    def _summarize_frame_extraction(self) -> Dict[str, Any]:
        """Summarize frame extraction results."""
        if not self.results['frame_extraction']:
            return {}
        
        df = pd.DataFrame(self.results['frame_extraction'])
        successful = df[df['success'] == True]
        
        if len(successful) == 0:
            return {'error': 'No successful extractions'}
        
        return {
            'total_extractions': len(df),
            'successful_extractions': len(successful),
            'success_rate': len(successful) / len(df),
            'avg_processing_time': successful['processing_time'].mean(),
            'avg_extraction_rate': successful['extraction_rate_fps'].mean(),
            'avg_memory_usage': successful['memory_used_mb'].mean()
        }
    
    def _generate_visualizations(self) -> None:
        """Generate performance visualization charts."""
        try:
            # Video search performance chart
            if self.results['video_search']:
                self._plot_video_search_performance()
            
            # Frame extraction performance chart
            if self.results['frame_extraction']:
                self._plot_frame_extraction_performance()
            
        except Exception as e:
            print(f"⚠️ Visualization generation failed: {e}")
    
    def _plot_video_search_performance(self) -> None:
        """Plot video search performance metrics."""
        df = pd.DataFrame(self.results['video_search'])
        successful = df[df['success'] == True]
        
        if len(successful) == 0:
            return
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('Video Search Performance Analysis', fontsize=16)
        
        # Processing time vs video size
        axes[0, 0].scatter(successful['video_size_mb'], successful['processing_time'])
        axes[0, 0].set_xlabel('Video Size (MB)')
        axes[0, 0].set_ylabel('Processing Time (s)')
        axes[0, 0].set_title('Processing Time vs Video Size')
        
        # Memory usage vs video size
        axes[0, 1].scatter(successful['video_size_mb'], successful['memory_used_mb'])
        axes[0, 1].set_xlabel('Video Size (MB)')
        axes[0, 1].set_ylabel('Memory Used (MB)')
        axes[0, 1].set_title('Memory Usage vs Video Size')
        
        # Matches found distribution
        axes[1, 0].hist(successful['matches_found'], bins=20)
        axes[1, 0].set_xlabel('Matches Found')
        axes[1, 0].set_ylabel('Frequency')
        axes[1, 0].set_title('Distribution of Matches Found')
        
        # Processing time distribution
        axes[1, 1].hist(successful['processing_time'], bins=20)
        axes[1, 1].set_xlabel('Processing Time (s)')
        axes[1, 1].set_ylabel('Frequency')
        axes[1, 1].set_title('Processing Time Distribution')
        
        plt.tight_layout()
        plt.savefig(self.output_dir / 'video_search_performance.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    def _plot_frame_extraction_performance(self) -> None:
        """Plot frame extraction performance metrics."""
        df = pd.DataFrame(self.results['frame_extraction'])
        successful = df[df['success'] == True]
        
        if len(successful) == 0:
            return
        
        fig, axes = plt.subplots(1, 2, figsize=(12, 5))
        fig.suptitle('Frame Extraction Performance Analysis', fontsize=16)
        
        # Extraction rate vs frame interval
        axes[0].scatter(successful['frame_interval'], successful['extraction_rate_fps'])
        axes[0].set_xlabel('Frame Interval')
        axes[0].set_ylabel('Extraction Rate (FPS)')
        axes[0].set_title('Extraction Rate vs Frame Interval')
        
        # Processing time vs video duration
        axes[1].scatter(successful['video_duration'], successful['processing_time'])
        axes[1].set_xlabel('Video Duration (s)')
        axes[1].set_ylabel('Processing Time (s)')
        axes[1].set_title('Processing Time vs Video Duration')
        
        plt.tight_layout()
        plt.savefig(self.output_dir / 'frame_extraction_performance.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    def cleanup_test_videos(self, test_videos: List[str]) -> None:
        """Clean up test videos."""
        print("\n🧹 Cleaning up test videos...")
        for video_path in test_videos:
            if os.path.exists(video_path):
                os.remove(video_path)
                print(f"   🗑️ Removed: {Path(video_path).name}")


def main():
    """Run comprehensive performance benchmark."""
    print("🎬 AI Video Search - Performance Benchmark Suite")
    print("=" * 60)
    
    # Initialize benchmark
    benchmark = PerformanceBenchmark()
    
    try:
        # Create test videos
        test_videos = benchmark.create_test_videos(num_videos=3)  # Reduced for faster testing
        
        # Run benchmarks
        benchmark.benchmark_frame_extraction(test_videos)
        benchmark.benchmark_video_search(test_videos)
        
        # Generate report
        benchmark.generate_report()
        
        print("\n✅ Benchmark completed successfully!")
        print(f"📊 Results saved to: {benchmark.output_dir}")
        
    except KeyboardInterrupt:
        print("\n⚠️ Benchmark interrupted by user")
    except Exception as e:
        print(f"\n❌ Benchmark failed: {e}")
    finally:
        # Cleanup
        if 'test_videos' in locals():
            benchmark.cleanup_test_videos(test_videos)


if __name__ == "__main__":
    main()
