#!/usr/bin/env python3
"""
Setup script to configure FFmpeg for the application.
This script helps set up FFmpeg when it's not in the system PATH.
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def find_ffmpeg():
    """Find FFmpeg executable in various locations."""
    
    # Check if ffmpeg is in PATH
    if shutil.which('ffmpeg'):
        return shutil.which('ffmpeg')
    
    # Check local ffmpeg folder
    local_ffmpeg = Path('ffmpeg/ffmpeg.exe')
    if local_ffmpeg.exists():
        return str(local_ffmpeg.absolute())
    
    # Check common installation locations
    common_paths = [
        'C:/ffmpeg/bin/ffmpeg.exe',
        'C:/Program Files/ffmpeg/bin/ffmpeg.exe',
        'C:/ProgramData/chocolatey/lib/ffmpeg/tools/ffmpeg/bin/ffmpeg.exe',
        Path.home() / 'ffmpeg/bin/ffmpeg.exe',
    ]
    
    for path in common_paths:
        if Path(path).exists():
            return str(Path(path).absolute())
    
    return None

def test_ffmpeg(ffmpeg_path):
    """Test if FFmpeg works."""
    try:
        result = subprocess.run([ffmpeg_path, '-version'], 
                              capture_output=True, text=True, timeout=10)
        return result.returncode == 0
    except Exception:
        return False

def setup_ffmpeg_environment():
    """Set up FFmpeg environment variable."""
    ffmpeg_path = find_ffmpeg()
    
    if ffmpeg_path:
        print(f"✅ Found FFmpeg at: {ffmpeg_path}")
        
        if test_ffmpeg(ffmpeg_path):
            print("✅ FFmpeg is working correctly")
            
            # Set environment variable for this session
            ffmpeg_dir = str(Path(ffmpeg_path).parent)
            current_path = os.environ.get('PATH', '')
            if ffmpeg_dir not in current_path:
                os.environ['PATH'] = ffmpeg_dir + os.pathsep + current_path
                print(f"✅ Added FFmpeg to PATH for this session: {ffmpeg_dir}")
            
            return True
        else:
            print(f"❌ FFmpeg found but not working: {ffmpeg_path}")
            return False
    else:
        print("❌ FFmpeg not found")
        print("\n💡 To fix this:")
        print("1. Download FFmpeg from: https://www.gyan.dev/ffmpeg/builds/")
        print("2. Extract ffmpeg.exe to: ffmpeg/ffmpeg.exe (in this folder)")
        print("3. Run this script again")
        return False

def main():
    """Main setup function."""
    print("🔧 FFmpeg Setup for AI-Powered Video Content Search")
    print("=" * 60)
    
    if setup_ffmpeg_environment():
        print("\n🎉 FFmpeg setup successful!")
        print("\n🚀 You can now run:")
        print("   python test_installation.py")
        print("   python main.py --web")
        return True
    else:
        print("\n❌ FFmpeg setup failed")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
