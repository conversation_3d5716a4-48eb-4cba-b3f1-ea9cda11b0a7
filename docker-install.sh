#!/bin/bash
# AI-Powered Video Content Search - Docker Installation Script
# Comprehensive Docker-based installation with multiple deployment options

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
BOLD='\033[1m'
NC='\033[0m'

# Global variables
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
LOG_FILE="$SCRIPT_DIR/docker-install.log"
ERROR_COUNT=0

# Configuration
INSTALL_GPU=false
ENABLE_REDIS=false
ENABLE_DATABASE=false
ENABLE_NGINX=false
ENABLE_MONITORING=false
PRODUCTION_MODE=false

# Functions
print_header() {
    echo -e "${PURPLE}${BOLD}========================================================================"
    echo -e "🐳 AI-Powered Video Content Search - Docker Installation"
    echo -e "========================================================================${NC}"
    echo
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
    echo "$(date): SUCCESS - $1" >> "$LOG_FILE"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
    echo "$(date): WARNING - $1" >> "$LOG_FILE"
    ((ERROR_COUNT++))
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
    echo "$(date): ERROR - $1" >> "$LOG_FILE"
    ((ERROR_COUNT++))
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
    echo "$(date): INFO - $1" >> "$LOG_FILE"
}

print_step() {
    echo -e "${CYAN}${BOLD}🔧 $1${NC}"
}

command_exists() {
    command -v "$1" >/dev/null 2>&1
}

check_docker() {
    print_step "Checking Docker installation"
    
    if ! command_exists docker; then
        print_error "Docker is not installed"
        echo "Please install Docker first:"
        echo "  - Ubuntu/Debian: curl -fsSL https://get.docker.com | sh"
        echo "  - CentOS/RHEL: curl -fsSL https://get.docker.com | sh"
        echo "  - macOS: Download Docker Desktop from https://docker.com"
        echo "  - Windows: Download Docker Desktop from https://docker.com"
        return 1
    fi
    
    if ! docker --version >/dev/null 2>&1; then
        print_error "Docker is installed but not running"
        echo "Please start Docker service:"
        echo "  - Linux: sudo systemctl start docker"
        echo "  - macOS/Windows: Start Docker Desktop"
        return 1
    fi
    
    print_success "Docker is installed and running"
    
    # Check Docker Compose
    if command_exists docker-compose; then
        print_success "Docker Compose is available"
    elif docker compose version >/dev/null 2>&1; then
        print_success "Docker Compose (plugin) is available"
    else
        print_error "Docker Compose is not available"
        echo "Please install Docker Compose:"
        echo "  - Linux: sudo curl -L \"https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-\$(uname -s)-\$(uname -m)\" -o /usr/local/bin/docker-compose"
        echo "  - macOS/Windows: Included with Docker Desktop"
        return 1
    fi
    
    return 0
}

check_gpu_support() {
    print_step "Checking GPU support"
    
    if command_exists nvidia-smi; then
        if nvidia-smi >/dev/null 2>&1; then
            print_success "NVIDIA GPU detected"
            echo "Would you like to enable GPU acceleration? (y/N)"
            read -r response
            if [[ "$response" =~ ^[Yy]$ ]]; then
                INSTALL_GPU=true
                print_info "GPU acceleration will be enabled"
                
                # Check for nvidia-docker
                if command_exists nvidia-docker; then
                    print_success "nvidia-docker is available"
                elif docker info 2>/dev/null | grep -q nvidia; then
                    print_success "NVIDIA Container Toolkit is available"
                else
                    print_warning "NVIDIA Container Toolkit not found"
                    echo "For GPU support, install NVIDIA Container Toolkit:"
                    echo "  https://docs.nvidia.com/datacenter/cloud-native/container-toolkit/install-guide.html"
                fi
            fi
        else
            print_warning "NVIDIA GPU detected but drivers may not be working"
        fi
    else
        print_info "No NVIDIA GPU detected (CPU-only mode)"
    fi
}

interactive_configuration() {
    print_step "Interactive Configuration"
    
    echo "Choose installation type:"
    echo "1) Basic (Web interface only)"
    echo "2) Standard (Web interface + Redis cache)"
    echo "3) Advanced (Web interface + Redis + Database)"
    echo "4) Production (All services + Nginx + Monitoring)"
    echo "5) Custom (Choose individual services)"
    
    read -p "Enter choice (1-5): " choice
    
    case $choice in
        1)
            print_info "Basic installation selected"
            ;;
        2)
            print_info "Standard installation selected"
            ENABLE_REDIS=true
            ;;
        3)
            print_info "Advanced installation selected"
            ENABLE_REDIS=true
            ENABLE_DATABASE=true
            ;;
        4)
            print_info "Production installation selected"
            ENABLE_REDIS=true
            ENABLE_DATABASE=true
            ENABLE_NGINX=true
            ENABLE_MONITORING=true
            PRODUCTION_MODE=true
            ;;
        5)
            print_info "Custom installation selected"
            
            echo "Enable Redis cache? (y/N)"
            read -r response
            [[ "$response" =~ ^[Yy]$ ]] && ENABLE_REDIS=true
            
            echo "Enable PostgreSQL database? (y/N)"
            read -r response
            [[ "$response" =~ ^[Yy]$ ]] && ENABLE_DATABASE=true
            
            echo "Enable Nginx reverse proxy? (y/N)"
            read -r response
            [[ "$response" =~ ^[Yy]$ ]] && ENABLE_NGINX=true
            
            echo "Enable monitoring (Prometheus + Grafana)? (y/N)"
            read -r response
            [[ "$response" =~ ^[Yy]$ ]] && ENABLE_MONITORING=true
            ;;
        *)
            print_warning "Invalid choice, using basic installation"
            ;;
    esac
}

create_environment_file() {
    print_step "Creating environment configuration"
    
    if [ ! -f .env ]; then
        if [ -f .env.example ]; then
            cp .env.example .env
            print_success "Created .env from .env.example"
        else
            print_warning ".env.example not found, creating basic .env"
            cat > .env << EOF
# Basic Docker configuration
INSTALL_GPU=$INSTALL_GPU
BUILD_ENV=$([ "$PRODUCTION_MODE" = true ] && echo "production" || echo "development")
WEB_PORT=8501
LOG_LEVEL=INFO
VIDEO_INPUT_DIR=./videos
EOF
        fi
    else
        print_info ".env file already exists"
    fi
    
    # Update GPU setting in .env
    if [ "$INSTALL_GPU" = true ]; then
        sed -i.bak 's/INSTALL_GPU=false/INSTALL_GPU=true/' .env
        print_info "Updated GPU setting in .env"
    fi
}

create_directories() {
    print_step "Creating necessary directories"
    
    directories=(
        "data"
        "static/output_clips"
        "temp_videos"
        "logs"
        "models/cache"
        "videos"
    )
    
    for dir in "${directories[@]}"; do
        if [ ! -d "$dir" ]; then
            mkdir -p "$dir"
            print_success "Created directory: $dir"
        else
            print_info "Directory exists: $dir"
        fi
    done
}

build_docker_image() {
    print_step "Building Docker image"
    
    build_args=""
    if [ "$INSTALL_GPU" = true ]; then
        build_args="--build-arg INSTALL_GPU=true"
    fi
    
    if [ "$PRODUCTION_MODE" = true ]; then
        build_args="$build_args --build-arg BUILD_ENV=production"
    fi
    
    echo "Building Docker image (this may take several minutes)..."
    if docker build $build_args -t ai-video-search:latest . >> "$LOG_FILE" 2>&1; then
        print_success "Docker image built successfully"
    else
        print_error "Failed to build Docker image"
        echo "Check $LOG_FILE for details"
        return 1
    fi
}

start_services() {
    print_step "Starting Docker services"
    
    # Build compose command with profiles
    compose_cmd="docker-compose"
    if command_exists docker && docker compose version >/dev/null 2>&1; then
        compose_cmd="docker compose"
    fi
    
    profiles=""
    if [ "$ENABLE_REDIS" = true ]; then
        profiles="$profiles --profile with-redis"
    fi
    if [ "$ENABLE_DATABASE" = true ]; then
        profiles="$profiles --profile with-database"
    fi
    if [ "$ENABLE_NGINX" = true ]; then
        profiles="$profiles --profile with-nginx"
    fi
    if [ "$ENABLE_MONITORING" = true ]; then
        profiles="$profiles --profile with-monitoring"
    fi
    
    echo "Starting services with command: $compose_cmd $profiles up -d"
    
    if $compose_cmd $profiles up -d >> "$LOG_FILE" 2>&1; then
        print_success "All services started successfully"
    else
        print_error "Failed to start some services"
        echo "Check $LOG_FILE for details"
        return 1
    fi
}

wait_for_services() {
    print_step "Waiting for services to be ready"
    
    echo "Waiting for main application..."
    timeout=60
    while [ $timeout -gt 0 ]; do
        if curl -f http://localhost:8501/_stcore/health >/dev/null 2>&1; then
            print_success "Main application is ready"
            break
        fi
        sleep 2
        ((timeout-=2))
    done
    
    if [ $timeout -le 0 ]; then
        print_warning "Main application health check timed out"
    fi
}

print_access_info() {
    print_step "Access Information"
    
    echo
    echo -e "${GREEN}🎉 Installation completed successfully!${NC}"
    echo
    echo -e "${CYAN}Access URLs:${NC}"
    echo "  🌐 Web Interface: http://localhost:8501"
    
    if [ "$ENABLE_REDIS" = true ]; then
        echo "  📊 Redis: localhost:6379"
    fi
    
    if [ "$ENABLE_DATABASE" = true ]; then
        echo "  🗄️  PostgreSQL: localhost:5432"
    fi
    
    if [ "$ENABLE_NGINX" = true ]; then
        echo "  🔀 Nginx: http://localhost:80"
    fi
    
    if [ "$ENABLE_MONITORING" = true ]; then
        echo "  📈 Prometheus: http://localhost:9090"
        echo "  📊 Grafana: http://localhost:3000 (admin/admin)"
    fi
    
    echo
    echo -e "${CYAN}Useful Commands:${NC}"
    echo "  📋 View logs: docker-compose logs -f"
    echo "  🔄 Restart: docker-compose restart"
    echo "  🛑 Stop: docker-compose down"
    echo "  🗑️  Remove: docker-compose down -v --rmi all"
    echo
    echo -e "${CYAN}Data Directories:${NC}"
    echo "  📁 Videos: ./videos/ (place your video files here)"
    echo "  📁 Output: ./static/output_clips/"
    echo "  📁 Logs: ./logs/"
    echo
}

cleanup_on_error() {
    if [ $ERROR_COUNT -gt 0 ]; then
        echo
        print_warning "Installation completed with $ERROR_COUNT warnings/errors"
        echo "Check $LOG_FILE for details"
        echo
        echo "To troubleshoot:"
        echo "  1. Check Docker logs: docker-compose logs"
        echo "  2. Verify system requirements"
        echo "  3. Check network connectivity"
        echo "  4. Review installation log: $LOG_FILE"
    fi
}

main() {
    # Initialize log file
    echo "Docker installation started at $(date)" > "$LOG_FILE"
    
    print_header
    
    # Check prerequisites
    if ! check_docker; then
        exit 1
    fi
    
    check_gpu_support
    interactive_configuration
    create_environment_file
    create_directories
    
    # Build and start
    if ! build_docker_image; then
        exit 1
    fi
    
    if ! start_services; then
        exit 1
    fi
    
    wait_for_services
    print_access_info
    cleanup_on_error
    
    echo "Installation log saved to: $LOG_FILE"
}

# Handle interruption
trap 'echo -e "\n${YELLOW}Installation interrupted${NC}"; exit 1' INT

# Run main function
main "$@"
