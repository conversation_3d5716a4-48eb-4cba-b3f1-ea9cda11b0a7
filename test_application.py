"""
Test script for the AI-powered video content search application.
This script tests the core functionality without requiring a video file.
"""

import os
import sys
import numpy as np
from PIL import Image
import tempfile
import cv2

# Add current directory to path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

try:
    from utils.frame_extraction import FrameExtractor
    from models.clip_model import CLIPMatcher
    from utils.video_slicing import VideoClipper
    from utils.clip_match import VideoSearchEngine
except ImportError as e:
    print(f"Import error: {e}")
    print("Trying alternative import method...")

    # Alternative import method
    import importlib.util

    def import_module_from_path(module_name, file_path):
        spec = importlib.util.spec_from_file_location(module_name, file_path)
        module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(module)
        return module

    # Import modules directly
    frame_extraction = import_module_from_path("frame_extraction", os.path.join(current_dir, "utils", "frame_extraction.py"))
    FrameExtractor = frame_extraction.FrameExtractor

    clip_model = import_module_from_path("clip_model", os.path.join(current_dir, "models", "clip_model.py"))
    CLIPMatcher = clip_model.CLIPMatcher

    video_slicing = import_module_from_path("video_slicing", os.path.join(current_dir, "utils", "video_slicing.py"))
    VideoClipper = video_slicing.VideoClipper

    clip_match = import_module_from_path("clip_match", os.path.join(current_dir, "utils", "clip_match.py"))
    VideoSearchEngine = clip_match.VideoSearchEngine


def create_test_video(output_path: str, duration_seconds: int = 10, fps: int = 30):
    """
    Create a simple test video with colored frames.
    
    Args:
        output_path: Path to save the test video
        duration_seconds: Duration of the video
        fps: Frames per second
    """
    print(f"Creating test video: {output_path}")
    
    # Video properties
    width, height = 640, 480
    total_frames = duration_seconds * fps
    
    # Create video writer
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
    
    # Generate frames with different colors
    colors = [
        (255, 0, 0),    # Red
        (0, 255, 0),    # Green
        (0, 0, 255),    # Blue
        (255, 255, 0),  # Yellow
        (255, 0, 255),  # Magenta
        (0, 255, 255),  # Cyan
    ]
    
    for frame_idx in range(total_frames):
        # Create a colored frame
        color_idx = (frame_idx // (fps * 2)) % len(colors)  # Change color every 2 seconds
        color = colors[color_idx]
        
        # Create frame
        frame = np.full((height, width, 3), color, dtype=np.uint8)
        
        # Add some text
        text = f"Frame {frame_idx} - Color {color_idx}"
        cv2.putText(frame, text, (50, 50), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
        
        # Add a moving circle
        circle_x = int((frame_idx % (fps * 2)) * width / (fps * 2))
        circle_y = height // 2
        cv2.circle(frame, (circle_x, circle_y), 30, (255, 255, 255), -1)
        
        out.write(frame)
    
    out.release()
    print(f"Test video created successfully: {output_path}")


def test_frame_extraction():
    """Test the frame extraction functionality."""
    print("\n" + "="*50)
    print("TESTING FRAME EXTRACTION")
    print("="*50)
    
    # Create a test video
    with tempfile.NamedTemporaryFile(suffix='.mp4', delete=False) as tmp_file:
        test_video_path = tmp_file.name
    
    try:
        create_test_video(test_video_path, duration_seconds=5, fps=30)
        
        # Test frame extraction
        extractor = FrameExtractor(every_n_frames=30)  # Extract 1 frame per second
        
        # Get video info
        info = extractor.get_video_info(test_video_path)
        print(f"✅ Video info: {info}")
        
        # Extract frames
        frames = extractor.extract_frames(test_video_path)
        print(f"✅ Extracted {len(frames)} frames")
        
        # Test time-based extraction
        frames_by_time = extractor.extract_frames_by_time(test_video_path, interval_seconds=1.0)
        print(f"✅ Extracted {len(frames_by_time)} frames by time")
        
        return frames
        
    except Exception as e:
        print(f"❌ Frame extraction test failed: {e}")
        return []
    
    finally:
        # Clean up
        if os.path.exists(test_video_path):
            os.unlink(test_video_path)


def test_clip_model():
    """Test the CLIP model functionality."""
    print("\n" + "="*50)
    print("TESTING CLIP MODEL")
    print("="*50)
    
    try:
        # Create CLIP matcher
        matcher = CLIPMatcher()
        print("✅ CLIP model loaded successfully")
        
        # Create test images
        test_images = []
        
        # Red image
        red_image = np.full((224, 224, 3), [255, 0, 0], dtype=np.uint8)
        test_images.append(red_image)
        
        # Blue image
        blue_image = np.full((224, 224, 3), [0, 0, 255], dtype=np.uint8)
        test_images.append(blue_image)
        
        # Green image
        green_image = np.full((224, 224, 3), [0, 255, 0], dtype=np.uint8)
        test_images.append(green_image)
        
        print(f"✅ Created {len(test_images)} test images")
        
        # Test text encoding
        text_features = matcher.encode_text("red color")
        print(f"✅ Text encoding shape: {text_features.shape}")
        
        # Test image encoding
        image_features = matcher.encode_images(test_images)
        print(f"✅ Image encoding shape: {image_features.shape}")
        
        # Test similarity computation
        similarities = matcher.compute_similarity(text_features, image_features)
        print(f"✅ Similarities: {similarities.cpu().numpy()}")
        
        # The red image should have the highest similarity to "red color"
        best_match_idx = similarities.argmax().item()
        print(f"✅ Best match index: {best_match_idx} (should be 0 for red image)")
        
        return True
        
    except Exception as e:
        print(f"❌ CLIP model test failed: {e}")
        return False


def test_search_engine():
    """Test the complete search engine."""
    print("\n" + "="*50)
    print("TESTING SEARCH ENGINE")
    print("="*50)
    
    # Create a test video
    with tempfile.NamedTemporaryFile(suffix='.mp4', delete=False) as tmp_file:
        test_video_path = tmp_file.name
    
    try:
        create_test_video(test_video_path, duration_seconds=6, fps=30)
        
        # Create search engine
        search_engine = VideoSearchEngine(
            frame_interval=30,  # 1 frame per second
            output_dir="test_output"
        )
        print("✅ Search engine created")
        
        # Test search
        results = search_engine.search_video(
            video_path=test_video_path,
            query="red color",
            similarity_threshold=0.1,
            top_k=10,
            create_clips=True,
            create_thumbnails=True
        )
        
        print(f"✅ Search completed")
        print(f"   - Processing time: {results['processing_time']:.2f}s")
        print(f"   - Matches found: {len(results['matches'])}")
        print(f"   - Clips created: {len(results['clips'])}")
        print(f"   - Thumbnails created: {len(results['thumbnails'])}")
        
        # Test batch search
        batch_results = search_engine.batch_search(
            video_path=test_video_path,
            queries=["red color", "blue color", "green color"],
            similarity_threshold=0.1
        )
        
        print(f"✅ Batch search completed")
        for query, result in batch_results.items():
            print(f"   - '{query}': {len(result['matches'])} matches")
        
        return True
        
    except Exception as e:
        print(f"❌ Search engine test failed: {e}")
        return False
    
    finally:
        # Clean up
        if os.path.exists(test_video_path):
            os.unlink(test_video_path)


def test_video_clipper():
    """Test video clip generation."""
    print("\n" + "="*50)
    print("TESTING VIDEO CLIPPER")
    print("="*50)
    
    # Create a test video
    with tempfile.NamedTemporaryFile(suffix='.mp4', delete=False) as tmp_file:
        test_video_path = tmp_file.name
    
    try:
        create_test_video(test_video_path, duration_seconds=10, fps=30)
        
        # Create video clipper
        clipper = VideoClipper(output_dir="test_output")
        print("✅ Video clipper created")
        
        # Test clip creation
        clip_path = clipper.create_clip_from_timestamps(
            video_path=test_video_path,
            start_time=2.0,
            end_time=5.0,
            output_filename="test_clip.mp4"
        )
        
        if clip_path and os.path.exists(clip_path):
            print(f"✅ Clip created: {clip_path}")
        else:
            print("❌ Clip creation failed")
        
        # Test thumbnail extraction
        # Create fake matches for testing
        fake_matches = [
            (30, np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8), 1.0, 0.8),
            (60, np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8), 2.0, 0.7),
            (90, np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8), 3.0, 0.6),
        ]
        
        thumbnails = clipper.extract_frame_thumbnails(fake_matches)
        print(f"✅ Created {len(thumbnails)} thumbnails")
        
        return True
        
    except Exception as e:
        print(f"❌ Video clipper test failed: {e}")
        return False
    
    finally:
        # Clean up
        if os.path.exists(test_video_path):
            os.unlink(test_video_path)


def run_all_tests():
    """Run all tests."""
    print("🧪 RUNNING AI VIDEO SEARCH TESTS")
    print("="*60)
    
    test_results = []
    
    # Test frame extraction
    frames = test_frame_extraction()
    test_results.append(("Frame Extraction", len(frames) > 0))
    
    # Test CLIP model
    clip_success = test_clip_model()
    test_results.append(("CLIP Model", clip_success))
    
    # Test video clipper
    clipper_success = test_video_clipper()
    test_results.append(("Video Clipper", clipper_success))
    
    # Test search engine (only if CLIP model works)
    if clip_success:
        search_success = test_search_engine()
        test_results.append(("Search Engine", search_success))
    else:
        test_results.append(("Search Engine", False))
    
    # Print results
    print("\n" + "="*60)
    print("TEST RESULTS")
    print("="*60)
    
    all_passed = True
    for test_name, passed in test_results:
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{test_name:<20} {status}")
        if not passed:
            all_passed = False
    
    print("="*60)
    if all_passed:
        print("🎉 ALL TESTS PASSED! The application is ready to use.")
        print("\n💡 Next steps:")
        print("   1. Run 'python main.py --web' to start the web interface")
        print("   2. Upload a video and try searching for objects")
        print("   3. Experiment with different similarity thresholds")
    else:
        print("⚠️  Some tests failed. Check the error messages above.")
        print("\n💡 Common issues:")
        print("   - Missing dependencies: pip install -r requirements.txt")
        print("   - GPU memory issues: The app will fall back to CPU")
        print("   - Network issues: CLIP model download may fail")
    
    return all_passed


if __name__ == "__main__":
    # Create output directory
    os.makedirs("test_output", exist_ok=True)
    
    try:
        success = run_all_tests()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️  Tests interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        sys.exit(1)
