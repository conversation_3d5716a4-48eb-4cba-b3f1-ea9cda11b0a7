#!/usr/bin/env python3
"""
Simple Application Test for AI-Powered Video Content Search.
Quick functionality test to verify the application is working correctly.
"""

import sys
import os
import traceback
from pathlib import Path

def test_imports():
    """Test if all required modules can be imported."""
    print("🔍 Testing imports...")
    
    required_modules = [
        ('torch', 'PyTorch'),
        ('cv2', 'OpenCV'),
        ('streamlit', 'Streamlit'),
        ('PIL', 'Pillow'),
        ('numpy', 'NumPy'),
        ('transformers', 'Transformers'),
    ]
    
    failed_imports = []
    
    for module, name in required_modules:
        try:
            __import__(module)
            print(f"   ✅ {name}")
        except ImportError as e:
            print(f"   ❌ {name}: {e}")
            failed_imports.append(name)
    
    if failed_imports:
        print(f"\n❌ Failed to import: {', '.join(failed_imports)}")
        print("💡 Try running: pip install -r requirements.txt")
        return False
    
    print("✅ All imports successful!")
    return True

def test_file_structure():
    """Test if required files and directories exist."""
    print("\n📁 Testing file structure...")
    
    required_files = [
        'main.py',
        'config.py',
        'requirements.txt',
    ]
    
    required_dirs = [
        'utils',
        'models',
        'app',
        'static',
    ]
    
    missing_items = []
    
    for file_path in required_files:
        if Path(file_path).exists():
            print(f"   ✅ {file_path}")
        else:
            print(f"   ❌ {file_path}")
            missing_items.append(file_path)
    
    for dir_path in required_dirs:
        if Path(dir_path).exists():
            print(f"   ✅ {dir_path}/")
        else:
            print(f"   ❌ {dir_path}/")
            missing_items.append(f"{dir_path}/")
    
    if missing_items:
        print(f"\n❌ Missing: {', '.join(missing_items)}")
        return False
    
    print("✅ File structure is correct!")
    return True

def test_core_modules():
    """Test if core application modules can be loaded."""
    print("\n⚙️ Testing core modules...")
    
    try:
        # Test configuration
        import config
        print("   ✅ Configuration module")
    except Exception as e:
        print(f"   ❌ Configuration module: {e}")
        return False
    
    try:
        # Test CLIP model
        from models.clip_model import CLIPMatcher
        print("   ✅ CLIP model module")
    except Exception as e:
        print(f"   ❌ CLIP model module: {e}")
        return False
    
    try:
        # Test frame extraction
        from utils.frame_extraction import FrameExtractor
        print("   ✅ Frame extraction module")
    except Exception as e:
        print(f"   ❌ Frame extraction module: {e}")
        return False
    
    try:
        # Test video search engine
        from utils.clip_match import VideoSearchEngine
        print("   ✅ Video search engine")
    except Exception as e:
        print(f"   ❌ Video search engine: {e}")
        return False
    
    try:
        # Test web interface
        from app.interface import main as web_main
        print("   ✅ Web interface module")
    except Exception as e:
        print(f"   ❌ Web interface module: {e}")
        return False
    
    print("✅ All core modules loaded successfully!")
    return True

def test_gpu_availability():
    """Test GPU availability (optional)."""
    print("\n🎮 Testing GPU availability...")
    
    try:
        import torch
        
        if torch.cuda.is_available():
            gpu_count = torch.cuda.device_count()
            gpu_name = torch.cuda.get_device_name(0)
            print(f"   ✅ CUDA available: {gpu_name} ({gpu_count} device(s))")
            return True
        elif hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
            print("   ✅ MPS (Apple Silicon) available")
            return True
        else:
            print("   ⚠️ No GPU acceleration available (CPU only)")
            return True  # Not an error, just a warning
    except Exception as e:
        print(f"   ❌ GPU testing failed: {e}")
        return False

def test_basic_functionality():
    """Test basic application functionality."""
    print("\n🧪 Testing basic functionality...")
    
    try:
        # Test creating a video search engine
        from utils.clip_match import VideoSearchEngine
        search_engine = VideoSearchEngine()
        print("   ✅ Video search engine creation")
        
        # Test CLIP model initialization
        from models.clip_model import CLIPMatcher
        clip_matcher = CLIPMatcher()
        print("   ✅ CLIP model initialization")
        
        # Test frame extractor
        from utils.frame_extraction import FrameExtractor
        extractor = FrameExtractor()
        print("   ✅ Frame extractor initialization")
        
        print("✅ Basic functionality test passed!")
        return True
        
    except Exception as e:
        print(f"   ❌ Basic functionality test failed: {e}")
        print(f"   📋 Error details: {traceback.format_exc()}")
        return False

def test_sample_video():
    """Test with a sample video if available."""
    print("\n🎬 Testing with sample video...")
    
    # Look for any video files in temp_videos directory
    video_extensions = ['.mp4', '.avi', '.mov', '.mkv', '.wmv']
    sample_videos = []
    
    if Path('temp_videos').exists():
        for ext in video_extensions:
            sample_videos.extend(Path('temp_videos').glob(f'*{ext}'))
    
    if not sample_videos:
        print("   ⚠️ No sample videos found in temp_videos/ directory")
        print("   💡 Add a video file to temp_videos/ to test video processing")
        return True  # Not an error, just no test video available
    
    # Use the first available video
    test_video = sample_videos[0]
    print(f"   📹 Found test video: {test_video.name}")
    
    try:
        from utils.clip_match import VideoSearchEngine
        
        search_engine = VideoSearchEngine()
        
        # Test basic video processing (just frame extraction)
        print("   🔄 Testing video processing...")
        results = search_engine.search_video(
            video_path=str(test_video),
            query="test",
            similarity_threshold=0.1,
            top_k=3,
            create_clips=False,
            create_thumbnails=False
        )
        
        if results['success']:
            print(f"   ✅ Video processing successful!")
            print(f"   📊 Found {len(results['matches'])} matches")
            print(f"   ⏱️ Processing time: {results['processing_time']:.2f}s")
        else:
            print(f"   ❌ Video processing failed: {results.get('error', 'Unknown error')}")
            return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ Sample video test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("🚀 AI-Powered Video Content Search - Application Test")
    print("=" * 60)
    
    tests = [
        ("Import Test", test_imports),
        ("File Structure Test", test_file_structure),
        ("Core Modules Test", test_core_modules),
        ("GPU Availability Test", test_gpu_availability),
        ("Basic Functionality Test", test_basic_functionality),
        ("Sample Video Test", test_sample_video),
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed_tests += 1
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
    
    print("\n" + "=" * 60)
    print("📋 Test Summary")
    print("=" * 60)
    
    success_rate = (passed_tests / total_tests) * 100
    
    if success_rate == 100:
        print("🎉 All tests passed! Application is ready to use.")
        print("\n🚀 Next steps:")
        print("   1. Start web interface: python main.py --web")
        print("   2. Try a video search: python main.py --video video.mp4 --query 'person'")
        print("   3. Check documentation: docs/quickstart.md")
    elif success_rate >= 80:
        print(f"⚠️ Most tests passed ({passed_tests}/{total_tests}). Some issues detected.")
        print("\n💡 Recommendations:")
        print("   1. Review the failed tests above")
        print("   2. Install missing dependencies if needed")
        print("   3. Check the troubleshooting guide: docs/troubleshooting.md")
    else:
        print(f"❌ Multiple tests failed ({passed_tests}/{total_tests}). Installation needs attention.")
        print("\n🔧 Next steps:")
        print("   1. Check installation: python test_installation.py")
        print("   2. Reinstall dependencies: pip install -r requirements.txt")
        print("   3. Review installation guide: INSTALLATION_GUIDE.md")
    
    print(f"\nTest Results: {passed_tests}/{total_tests} passed ({success_rate:.1f}%)")
    
    return success_rate >= 80

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️ Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1)
