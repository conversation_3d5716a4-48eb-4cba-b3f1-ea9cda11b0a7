"""
Configuration settings for AI-powered video content search.
Optimized for handling large video files efficiently.
"""

import os
from typing import <PERSON>ple, Optional


class VideoSearchConfig:
    """Configuration class for video search settings."""
    
    # Default settings for different video sizes
    SMALL_VIDEO_CONFIG = {
        "frame_interval": 15,           # Extract every 15 frames (high precision)
        "target_resolution": (720, 540), # High resolution
        "quality_factor": 1.0,          # No compression
        "batch_size": 8,                # Larger batch size
        "max_frames": None,             # No frame limit
        "use_chunked_processing": False
    }
    
    MEDIUM_VIDEO_CONFIG = {
        "frame_interval": 30,           # Extract every 30 frames (balanced)
        "target_resolution": (512, 384), # Medium resolution
        "quality_factor": 0.8,          # Light compression
        "batch_size": 4,                # Medium batch size
        "max_frames": 1000,             # Limit frames for memory
        "use_chunked_processing": True
    }
    
    LARGE_VIDEO_CONFIG = {
        "frame_interval": 60,           # Extract every 60 frames (fast)
        "target_resolution": (320, 240), # Low resolution
        "quality_factor": 0.6,          # More compression
        "batch_size": 2,                # Small batch size
        "max_frames": 500,              # Strict frame limit
        "use_chunked_processing": True
    }
    
    # Memory management settings
    MAX_CACHE_SIZE_MB = 2048           # 2GB cache limit
    CHUNK_SIZE = 200                   # Frames per chunk
    
    # File size thresholds (in MB)
    SMALL_VIDEO_THRESHOLD = 100        # < 100MB
    MEDIUM_VIDEO_THRESHOLD = 500       # 100MB - 500MB
    # > 500MB = large video
    
    @classmethod
    def get_config_for_video(cls, video_path: str) -> dict:
        """
        Get optimal configuration based on video file size.
        
        Args:
            video_path: Path to the video file
            
        Returns:
            Configuration dictionary
        """
        try:
            file_size_mb = os.path.getsize(video_path) / (1024 * 1024)
            
            if file_size_mb < cls.SMALL_VIDEO_THRESHOLD:
                print(f"Small video detected ({file_size_mb:.1f}MB) - Using high quality settings")
                return cls.SMALL_VIDEO_CONFIG.copy()
            elif file_size_mb < cls.MEDIUM_VIDEO_THRESHOLD:
                print(f"Medium video detected ({file_size_mb:.1f}MB) - Using balanced settings")
                return cls.MEDIUM_VIDEO_CONFIG.copy()
            else:
                print(f"Large video detected ({file_size_mb:.1f}MB) - Using optimized settings")
                return cls.LARGE_VIDEO_CONFIG.copy()
                
        except Exception:
            # Default to medium config if file size can't be determined
            print("Could not determine video size - Using balanced settings")
            return cls.MEDIUM_VIDEO_CONFIG.copy()
    
    @classmethod
    def get_config_for_duration(cls, duration_seconds: float) -> dict:
        """
        Get optimal configuration based on video duration.
        
        Args:
            duration_seconds: Video duration in seconds
            
        Returns:
            Configuration dictionary
        """
        if duration_seconds < 300:  # < 5 minutes
            print(f"Short video ({duration_seconds/60:.1f} min) - Using high quality settings")
            return cls.SMALL_VIDEO_CONFIG.copy()
        elif duration_seconds < 1800:  # 5-30 minutes
            print(f"Medium video ({duration_seconds/60:.1f} min) - Using balanced settings")
            return cls.MEDIUM_VIDEO_CONFIG.copy()
        else:  # > 30 minutes
            print(f"Long video ({duration_seconds/60:.1f} min) - Using optimized settings")
            return cls.LARGE_VIDEO_CONFIG.copy()
    
    @classmethod
    def get_adaptive_config(cls, video_path: str, video_info: dict) -> dict:
        """
        Get adaptive configuration based on both file size and duration.
        
        Args:
            video_path: Path to the video file
            video_info: Video information dictionary
            
        Returns:
            Optimized configuration dictionary
        """
        # Get configs based on different criteria
        size_config = cls.get_config_for_video(video_path)
        duration_config = cls.get_config_for_duration(video_info['duration_seconds'])
        
        # Use the more conservative (optimized) settings
        final_config = {}
        
        # Take the higher frame interval (less frames)
        final_config['frame_interval'] = max(
            size_config['frame_interval'], 
            duration_config['frame_interval']
        )
        
        # Take the lower resolution (less memory)
        size_res = size_config['target_resolution']
        duration_res = duration_config['target_resolution']
        
        if size_res and duration_res:
            # Choose smaller resolution
            if size_res[0] * size_res[1] <= duration_res[0] * duration_res[1]:
                final_config['target_resolution'] = size_res
            else:
                final_config['target_resolution'] = duration_res
        else:
            final_config['target_resolution'] = size_res or duration_res
        
        # Take the lower quality factor (more compression)
        final_config['quality_factor'] = min(
            size_config['quality_factor'],
            duration_config['quality_factor']
        )
        
        # Take the smaller batch size (less memory)
        final_config['batch_size'] = min(
            size_config['batch_size'],
            duration_config['batch_size']
        )
        
        # Take the smaller max frames (less memory)
        size_max = size_config['max_frames']
        duration_max = duration_config['max_frames']
        
        if size_max and duration_max:
            final_config['max_frames'] = min(size_max, duration_max)
        else:
            final_config['max_frames'] = size_max or duration_max
        
        # Use chunked processing if either config suggests it
        final_config['use_chunked_processing'] = (
            size_config['use_chunked_processing'] or 
            duration_config['use_chunked_processing']
        )
        
        print(f"Adaptive config: {final_config}")
        return final_config


# Preset configurations for common use cases
PRESETS = {
    "quality": {
        "name": "High Quality",
        "description": "Best quality, slower processing",
        "frame_interval": 15,
        "target_resolution": (720, 540),
        "quality_factor": 1.0,
        "batch_size": 8
    },
    
    "balanced": {
        "name": "Balanced",
        "description": "Good quality, reasonable speed",
        "frame_interval": 30,
        "target_resolution": (512, 384),
        "quality_factor": 0.8,
        "batch_size": 4
    },
    
    "speed": {
        "name": "Fast Processing",
        "description": "Lower quality, faster processing",
        "frame_interval": 60,
        "target_resolution": (320, 240),
        "quality_factor": 0.6,
        "batch_size": 2
    },
    
    "memory": {
        "name": "Memory Optimized",
        "description": "Minimal memory usage",
        "frame_interval": 90,
        "target_resolution": (256, 192),
        "quality_factor": 0.4,
        "batch_size": 1
    }
}


def get_preset_config(preset_name: str) -> dict:
    """
    Get configuration for a named preset.
    
    Args:
        preset_name: Name of the preset
        
    Returns:
        Configuration dictionary
    """
    if preset_name in PRESETS:
        return PRESETS[preset_name].copy()
    else:
        print(f"Unknown preset '{preset_name}', using balanced")
        return PRESETS["balanced"].copy()


def print_config_info(config: dict):
    """Print configuration information in a readable format."""
    print("\n📋 Configuration Settings:")
    print(f"   Frame Interval: {config.get('frame_interval', 'N/A')}")
    print(f"   Resolution: {config.get('target_resolution', 'Original')}")
    print(f"   Quality Factor: {config.get('quality_factor', 'N/A')}")
    print(f"   Batch Size: {config.get('batch_size', 'N/A')}")
    print(f"   Max Frames: {config.get('max_frames', 'Unlimited')}")
    print(f"   Chunked Processing: {config.get('use_chunked_processing', False)}")
    print("-" * 50)
