# 🌐 Live Streaming Detection Guide

## 🚀 **Real-Time AI Detection on Live Streams!**

The AI-powered video search application now supports **Live Streaming Detection** - connect to RTSP cameras, IP cameras, YouTube live streams, and any video stream for real-time object detection!

## ✨ **New Live Streaming Features**

### **🌐 Stream Source Support:**
- **RTSP Streams**: Security cameras, IP cameras, surveillance systems
- **HTTP/HTTPS Streams**: Web cameras, streaming servers, HLS streams
- **YouTube Live**: Live broadcasts, webcams, events
- **IP Cameras**: Network cameras with streaming capabilities
- **Custom Streams**: Any OpenCV-compatible video stream

### **🎯 Advanced Stream Processing:**
- **Low Latency**: Optimized buffer settings for real-time performance
- **Auto-Reconnection**: Handles network interruptions gracefully
- **Stream Resolution**: Automatic detection and optimization
- **Timeout Handling**: Smart connection and read timeouts

## 🎛️ **How to Use Live Streaming**

### **🌐 Web Interface:**

1. **Open the Application**
   - Go to http://localhost:8501
   - Click **"📹 Live Video Detection"** tab

2. **Select Live Stream**
   - Choose **"Live Stream"** as video source
   - Enter your stream URL in the text field

3. **Stream URL Examples:**
   ```
   RTSP: rtsp://*************:554/stream
   HTTP: http://*************:8080/video
   YouTube: https://youtube.com/watch?v=VIDEO_ID
   ```

4. **Configure Detection**
   - **Search Query**: "person", "car", "dog", etc.
   - **Detection Threshold**: 0.25-0.3 recommended
   - **Detection Interval**: 0.5 seconds for balanced performance

5. **Start Detection**
   - Click **"🚀 Start Detection"**
   - See live stream with real-time object detection
   - View extracted objects as they're detected

### **💻 Command Line Interface:**

```bash
# RTSP camera stream
python main.py --live "person" --stream "rtsp://*************:554/stream"

# HTTP stream
python main.py --live "car" --stream "http://*************:8080/video"

# YouTube live stream
python main.py --live "person" --stream "https://youtube.com/watch?v=VIDEO_ID"

# With custom settings
python main.py --live "dog" --stream "rtsp://camera.local/stream" --threshold 0.3 --detection-interval 0.5
```

## 📡 **Supported Stream Types**

### **🔒 RTSP Streams (Most Common):**
```
# Basic RTSP
rtsp://*************:554/stream

# With authentication
rtsp://username:password@*************:554/stream

# Different channels/streams
rtsp://*************:554/stream1
rtsp://*************:554/h264
rtsp://*************:554/live
```

### **🌐 HTTP/HTTPS Streams:**
```
# Direct video stream
http://*************:8080/video

# HLS streams
https://example.com/live.m3u8

# MJPEG streams
http://*************:8080/mjpeg
```

### **📺 YouTube Live Streams:**
```
# Live broadcasts
https://youtube.com/watch?v=VIDEO_ID

# Short URLs
https://youtu.be/VIDEO_ID

# Live events
https://youtube.com/watch?v=LIVE_EVENT_ID
```

### **📷 IP Camera Examples:**

**Popular IP Camera Brands:**
```
# Hikvision
rtsp://admin:password@*************:554/Streaming/Channels/101

# Dahua
rtsp://admin:password@*************:554/cam/realmonitor?channel=1&subtype=0

# Axis
rtsp://root:password@*************/axis-media/media.amp

# Foscam
rtsp://admin:password@*************:554/videoMain

# Generic
rtsp://admin:password@*************:554/stream
```

## 🎯 **Real-World Use Cases**

### **🏠 Home Security:**
```bash
# Monitor front door
python main.py --live "person" --stream "rtsp://*************:554/stream"

# Detect vehicles in driveway
python main.py --live "car" --stream "rtsp://driveway-cam.local/stream"
```

### **🏢 Business Monitoring:**
```bash
# Customer counting
python main.py --live "person" --stream "rtsp://store-cam:554/stream"

# Parking lot monitoring
python main.py --live "vehicle" --stream "rtsp://parking-cam:554/stream"
```

### **🐕 Pet Monitoring:**
```bash
# Watch pets at home
python main.py --live "dog" --stream "rtsp://pet-cam:554/stream"

# Monitor multiple animals
python main.py --live "animal" --stream "rtsp://yard-cam:554/stream"
```

### **🚗 Traffic Analysis:**
```bash
# Count vehicles
python main.py --live "car" --stream "rtsp://traffic-cam:554/stream"

# Detect specific vehicle types
python main.py --live "truck" --stream "rtsp://highway-cam:554/stream"
```

### **📺 YouTube Live Monitoring:**
```bash
# Monitor live events
python main.py --live "person" --stream "https://youtube.com/watch?v=LIVE_EVENT"

# Wildlife streams
python main.py --live "bird" --stream "https://youtube.com/watch?v=WILDLIFE_CAM"
```

## ⚙️ **Stream Configuration & Optimization**

### **🔧 Network Settings:**
- **Connection Timeout**: 10 seconds for initial connection
- **Read Timeout**: 5 seconds for frame reading
- **Buffer Size**: Minimized to 1 frame for low latency
- **Auto-Reconnection**: Handles temporary network issues

### **📊 Performance Optimization:**

**For Low Latency:**
```bash
# Fast detection (0.1-0.2 second intervals)
python main.py --live "person" --stream "rtsp://cam:554/stream" --detection-interval 0.1
```

**For Accuracy:**
```bash
# Higher threshold, slower detection
python main.py --live "person" --stream "rtsp://cam:554/stream" --threshold 0.35 --detection-interval 1.0
```

**For Bandwidth Optimization:**
- Use lower resolution streams when available
- Adjust detection interval based on network speed
- Consider using substreams for detection

### **🌐 Network Requirements:**
- **Minimum**: 1 Mbps for basic streams
- **Recommended**: 5+ Mbps for HD streams
- **Optimal**: 10+ Mbps for multiple streams
- **Latency**: < 100ms for real-time feel

## 🔧 **Troubleshooting**

### **Connection Issues:**
```
Problem: Cannot connect to stream
Solutions:
1. Check stream URL format
2. Verify network connectivity
3. Test with VLC media player first
4. Check firewall settings
5. Verify camera credentials
```

### **Authentication Problems:**
```
Problem: Access denied
Solutions:
1. Check username/password in URL
2. Use format: rtsp://user:pass@ip:port/stream
3. Verify camera user permissions
4. Try different authentication methods
```

### **Performance Issues:**
```
Problem: Laggy or slow detection
Solutions:
1. Increase detection interval (0.5-1.0 seconds)
2. Use lower resolution stream
3. Check network bandwidth
4. Reduce similarity threshold
5. Close other network applications
```

### **Stream Quality Issues:**
```
Problem: Poor video quality or dropouts
Solutions:
1. Check network stability
2. Use wired connection instead of WiFi
3. Verify camera stream settings
4. Try different stream URLs/formats
5. Check camera bandwidth limits
```

## 💡 **Pro Tips**

### **🎯 For Best Results:**
1. **Test Stream First**: Use VLC or similar to verify stream works
2. **Use Wired Network**: More stable than WiFi for continuous streaming
3. **Check Camera Settings**: Ensure stream is enabled and configured
4. **Monitor Bandwidth**: Multiple streams can overwhelm network
5. **Use Substreams**: Many cameras offer lower resolution substreams

### **⚡ Performance Tips:**
1. **Start Conservative**: Begin with 0.5-1.0 second detection intervals
2. **Monitor CPU Usage**: Adjust settings if CPU usage is too high
3. **Use Local Network**: Streams over internet may have higher latency
4. **Optimize Queries**: Specific queries ("red car") work better than generic ("object")

### **🔒 Security Considerations:**
1. **Change Default Passwords**: Never use default camera credentials
2. **Use VPN**: For remote access to cameras
3. **Network Segmentation**: Isolate cameras on separate network
4. **Regular Updates**: Keep camera firmware updated

## 📊 **Stream Status Monitoring**

The application provides real-time feedback:
- **Connection Status**: Shows if stream is connected
- **Frame Rate**: Displays actual FPS being processed
- **Detection Rate**: Shows detections per minute
- **Network Quality**: Indicates connection stability

## 🎉 **Success Stories**

### **Security Company:**
*"We connected 12 RTSP cameras to monitor a construction site. Real-time person detection alerts helped prevent multiple break-ins!"*

### **Retail Store:**
*"Using our existing IP cameras with 'person' detection, we can count customers in real-time and optimize staffing."*

### **Wildlife Researcher:**
*"Connected to YouTube wildlife streams to automatically detect and catalog different animal species 24/7."*

### **Smart Home User:**
*"My Hikvision doorbell camera now sends instant alerts when people approach, with cropped images of exactly who it is!"*

---

**🌐 Live Streaming Detection transforms any video stream into an intelligent monitoring system with real-time AI-powered object recognition!**
