"""
Advanced matching techniques to improve search accuracy and reduce false positives.
"""

import torch
import numpy as np
from typing import List, Tuple, Dict, Any, Optional
from PIL import Image
import cv2
from transformers import CLIPProcessor, CLIPModel


class AdvancedMatcher:
    """Enhanced matching with multiple techniques to improve accuracy."""
    
    def __init__(self, clip_model, clip_processor, device):
        self.clip_model = clip_model
        self.clip_processor = clip_processor
        self.device = device
    
    def multi_query_matching(self, images: List[np.ndarray], main_query: str, 
                           batch_size: int = 4) -> List[float]:
        """
        Use multiple related queries to improve matching accuracy.
        
        Args:
            images: List of image arrays
            main_query: Primary search query
            batch_size: Batch size for processing
            
        Returns:
            List of enhanced similarity scores
        """
        # Generate related queries
        related_queries = self._generate_related_queries(main_query)
        
        all_scores = []
        
        # Get scores for each query
        for query in related_queries:
            scores = self._compute_similarity_scores(images, query, batch_size)
            all_scores.append(scores)
        
        # Combine scores using weighted average
        weights = [1.0] + [0.3] * (len(related_queries) - 1)  # Main query gets highest weight
        
        enhanced_scores = np.zeros(len(images))
        for scores, weight in zip(all_scores, weights):
            enhanced_scores += np.array(scores) * weight
        
        # Normalize by total weight
        enhanced_scores /= sum(weights)
        
        return enhanced_scores.tolist()
    
    def _generate_related_queries(self, main_query: str) -> List[str]:
        """Generate related queries to improve matching."""
        queries = [main_query]  # Start with main query
        
        # Add variations based on common patterns
        words = main_query.lower().split()
        
        # Color + object patterns
        colors = ['red', 'blue', 'green', 'yellow', 'black', 'white', 'orange', 'purple']
        objects = ['car', 'person', 'dog', 'cat', 'tree', 'building', 'house', 'bike']
        
        if any(color in words for color in colors) and any(obj in words for obj in objects):
            # For "red car" -> add "car", "vehicle", "automobile"
            if 'car' in words:
                queries.extend([
                    main_query.replace('car', 'vehicle'),
                    main_query.replace('car', 'automobile'),
                    'car'
                ])
            elif 'person' in words:
                queries.extend([
                    main_query.replace('person', 'people'),
                    main_query.replace('person', 'human'),
                    'person'
                ])
        
        # Add broader categories
        if 'car' in main_query.lower():
            queries.extend(['vehicle', 'automobile', 'transportation'])
        elif 'person' in main_query.lower():
            queries.extend(['people', 'human', 'individual'])
        elif 'dog' in main_query.lower():
            queries.extend(['animal', 'pet', 'canine'])
        elif 'tree' in main_query.lower():
            queries.extend(['vegetation', 'plant', 'nature'])
        
        # Remove duplicates while preserving order
        seen = set()
        unique_queries = []
        for query in queries:
            if query not in seen:
                seen.add(query)
                unique_queries.append(query)
        
        return unique_queries[:5]  # Limit to 5 queries max
    
    def _compute_similarity_scores(self, images: List[np.ndarray], query: str, 
                                 batch_size: int) -> List[float]:
        """Compute similarity scores for a single query."""
        # Encode text
        text_inputs = self.clip_processor(text=[query], return_tensors="pt", padding=True)
        text_inputs = {k: v.to(self.device) for k, v in text_inputs.items()}
        
        with torch.no_grad():
            text_features = self.clip_model.get_text_features(**text_inputs)
            text_features = text_features / text_features.norm(dim=-1, keepdim=True)
        
        all_scores = []
        
        # Process images in batches
        for i in range(0, len(images), batch_size):
            batch_images = images[i:i + batch_size]
            pil_images = [Image.fromarray(img) for img in batch_images]
            
            with torch.no_grad():
                image_inputs = self.clip_processor(images=pil_images, return_tensors="pt", padding=True)
                image_inputs = {k: v.to(self.device) for k, v in image_inputs.items()}
                
                image_features = self.clip_model.get_image_features(**image_inputs)
                image_features = image_features / image_features.norm(dim=-1, keepdim=True)
                
                # Compute similarity
                similarities = torch.matmul(text_features, image_features.T)
                all_scores.extend(similarities.squeeze().cpu().numpy().tolist())
        
        return all_scores
    
    def negative_filtering(self, images: List[np.ndarray], query: str, 
                          scores: List[float], batch_size: int = 4) -> List[float]:
        """
        Filter out false positives using negative examples.
        
        Args:
            images: List of image arrays
            query: Search query
            scores: Current similarity scores
            batch_size: Batch size for processing
            
        Returns:
            Filtered similarity scores
        """
        # Generate negative queries (things we DON'T want)
        negative_queries = self._generate_negative_queries(query)
        
        if not negative_queries:
            return scores
        
        # Get negative scores
        negative_scores = []
        for neg_query in negative_queries:
            neg_scores = self._compute_similarity_scores(images, neg_query, batch_size)
            negative_scores.append(neg_scores)
        
        # Take maximum negative score for each image
        max_negative_scores = np.max(negative_scores, axis=0) if negative_scores else np.zeros(len(scores))
        
        # Penalize images that match negative queries
        filtered_scores = []
        for i, (pos_score, neg_score) in enumerate(zip(scores, max_negative_scores)):
            # If negative score is high, reduce positive score
            penalty = min(neg_score * 0.5, 0.3)  # Max penalty of 0.3
            filtered_score = max(0.0, pos_score - penalty)
            filtered_scores.append(filtered_score)
        
        return filtered_scores
    
    def _generate_negative_queries(self, main_query: str) -> List[str]:
        """Generate negative queries to filter out false positives."""
        negative_queries = []
        
        # Common false positives for different queries
        if 'car' in main_query.lower():
            negative_queries = ['building', 'tree', 'person walking', 'sky', 'road sign']
        elif 'person' in main_query.lower():
            negative_queries = ['car', 'building', 'tree', 'object', 'landscape']
        elif 'tree' in main_query.lower():
            negative_queries = ['building', 'car', 'person', 'indoor scene', 'sky only']
        elif 'building' in main_query.lower():
            negative_queries = ['tree', 'car', 'person', 'sky', 'landscape only']
        elif 'animal' in main_query.lower() or 'dog' in main_query.lower():
            negative_queries = ['person', 'car', 'building', 'tree', 'object']
        
        return negative_queries
    
    def contextual_scoring(self, images: List[np.ndarray], query: str, 
                          scores: List[float], batch_size: int = 4) -> List[float]:
        """
        Improve scoring by considering image context and composition.
        
        Args:
            images: List of image arrays
            query: Search query
            scores: Current similarity scores
            batch_size: Batch size for processing
            
        Returns:
            Context-enhanced similarity scores
        """
        # Analyze image properties
        enhanced_scores = []
        
        for i, (image, score) in enumerate(zip(images, scores)):
            context_bonus = 0.0
            
            # Check image quality and composition
            if self._is_good_composition(image):
                context_bonus += 0.05
            
            # Check if object is prominent in the image
            if self._has_prominent_subject(image, query):
                context_bonus += 0.1
            
            # Check color distribution relevance
            if self._has_relevant_colors(image, query):
                context_bonus += 0.05
            
            enhanced_score = min(1.0, score + context_bonus)
            enhanced_scores.append(enhanced_score)
        
        return enhanced_scores
    
    def _is_good_composition(self, image: np.ndarray) -> bool:
        """Check if image has good composition (not blurry, good contrast)."""
        # Convert to grayscale for analysis
        gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
        
        # Check for blur using Laplacian variance
        laplacian_var = cv2.Laplacian(gray, cv2.CV_64F).var()
        
        # Check contrast
        contrast = gray.std()
        
        return laplacian_var > 100 and contrast > 30
    
    def _has_prominent_subject(self, image: np.ndarray, query: str) -> bool:
        """Check if the subject is prominent in the image."""
        # Simple edge detection to find prominent objects
        gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
        edges = cv2.Canny(gray, 50, 150)
        
        # Calculate edge density in center region
        h, w = edges.shape
        center_region = edges[h//4:3*h//4, w//4:3*w//4]
        edge_density = np.sum(center_region > 0) / center_region.size
        
        return edge_density > 0.1  # Subject is prominent if many edges in center
    
    def _has_relevant_colors(self, image: np.ndarray, query: str) -> bool:
        """Check if image has colors relevant to the query."""
        query_lower = query.lower()
        
        # Define color ranges in HSV
        color_ranges = {
            'red': [(0, 50, 50), (10, 255, 255), (170, 50, 50), (180, 255, 255)],
            'blue': [(100, 50, 50), (130, 255, 255)],
            'green': [(40, 50, 50), (80, 255, 255)],
            'yellow': [(20, 50, 50), (30, 255, 255)],
            'orange': [(10, 50, 50), (20, 255, 255)],
            'purple': [(130, 50, 50), (170, 255, 255)]
        }
        
        # Check if query mentions a color
        for color_name, ranges in color_ranges.items():
            if color_name in query_lower:
                # Convert image to HSV
                hsv = cv2.cvtColor(image, cv2.COLOR_RGB2HSV)
                
                # Check for color presence
                for i in range(0, len(ranges), 2):
                    lower = np.array(ranges[i])
                    upper = np.array(ranges[i + 1])
                    mask = cv2.inRange(hsv, lower, upper)
                    
                    # If significant amount of the color is present
                    if np.sum(mask > 0) / mask.size > 0.05:  # 5% of image
                        return True
        
        return False  # No specific color mentioned or found


def create_advanced_matcher(clip_model, clip_processor, device) -> AdvancedMatcher:
    """Factory function to create an advanced matcher."""
    return AdvancedMatcher(clip_model, clip_processor, device)
