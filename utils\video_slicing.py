"""
Video clip generation utilities for creating clips from matching frames.
"""

import os
import cv2
from moviepy.video.io.VideoFileClip import VideoFileClip
from moviepy.video.fx import resize
from typing import List, Tuple, Optional
import numpy as np
from tqdm import tqdm
import tempfile
import shutil

try:
    from .object_extraction import ObjectExtractor
except ImportError:
    import sys
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from utils.object_extraction import ObjectExtractor


class VideoClipper:
    """Generate video clips from matching frames."""
    
    def __init__(self, output_dir: str = "static/output_clips"):
        """
        Initialize video clipper.

        Args:
            output_dir: Directory to save output clips
        """
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)

        # Initialize object extractor for cropping specific objects
        try:
            self.object_extractor = ObjectExtractor()
            self.has_object_extraction = True
            print("✅ Object extraction enabled")
        except Exception as e:
            print(f"⚠️ Object extraction disabled: {e}")
            self.has_object_extraction = False
    
    def create_clip_from_timestamps(self, 
                                  video_path: str, 
                                  start_time: float, 
                                  end_time: float, 
                                  output_filename: str,
                                  max_duration: float = 10.0) -> str:
        """
        Create a video clip from start and end timestamps.
        
        Args:
            video_path: Path to the source video
            start_time: Start time in seconds
            end_time: End time in seconds
            output_filename: Name for the output file
            max_duration: Maximum clip duration in seconds
            
        Returns:
            Path to the created clip
        """
        # Ensure clip doesn't exceed max duration
        if end_time - start_time > max_duration:
            end_time = start_time + max_duration
        
        output_path = os.path.join(self.output_dir, output_filename)
        
        try:
            with VideoFileClip(video_path) as video:
                clip = video.subclip(start_time, end_time)
                clip.write_videofile(output_path, codec="libx264", audio_codec="aac", verbose=False, logger=None)
            
            print(f"Created clip: {output_path} ({end_time - start_time:.2f}s)")
            return output_path
            
        except Exception as e:
            print(f"Error creating clip: {e}")
            return None
    
    def create_clips_from_matches(self, 
                                video_path: str, 
                                matches: List[Tuple[int, np.ndarray, float, float]],
                                clip_duration: float = 3.0,
                                min_interval: float = 1.0,
                                query_name: str = "search") -> List[str]:
        """
        Create video clips from matching frames.
        
        Args:
            video_path: Path to the source video
            matches: List of (frame_index, frame_array, timestamp, score) tuples
            clip_duration: Duration of each clip in seconds
            min_interval: Minimum interval between clips to avoid duplicates
            query_name: Name to include in output filenames
            
        Returns:
            List of paths to created clips
        """
        if not matches:
            return []
        
        # Get video info
        with VideoFileClip(video_path) as video:
            video_duration = video.duration
        
        # Group nearby matches to avoid creating overlapping clips
        grouped_matches = self._group_nearby_matches(matches, min_interval)
        
        clip_paths = []
        
        print(f"Creating {len(grouped_matches)} clips for query: {query_name}")
        
        for i, match_group in enumerate(tqdm(grouped_matches, desc="Creating clips")):
            # Use the best match in the group (highest score)
            best_match = max(match_group, key=lambda x: x[3])
            frame_idx, frame_array, timestamp, score = best_match
            
            # Calculate clip start and end times
            half_duration = clip_duration / 2
            start_time = max(0, timestamp - half_duration)
            end_time = min(video_duration, timestamp + half_duration)
            
            # Adjust if clip would be too short
            if end_time - start_time < clip_duration * 0.5:
                if start_time == 0:
                    end_time = min(video_duration, clip_duration)
                else:
                    start_time = max(0, video_duration - clip_duration)
            
            # Create output filename
            safe_query = "".join(c for c in query_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
            output_filename = f"{safe_query}_clip_{i+1}_score_{score:.3f}.mp4"
            
            # Create the clip
            clip_path = self.create_clip_from_timestamps(
                video_path, start_time, end_time, output_filename
            )
            
            if clip_path:
                clip_paths.append(clip_path)
        
        return clip_paths
    
    def _group_nearby_matches(self, 
                            matches: List[Tuple[int, np.ndarray, float, float]], 
                            min_interval: float) -> List[List[Tuple[int, np.ndarray, float, float]]]:
        """
        Group matches that are close in time to avoid creating overlapping clips.
        
        Args:
            matches: List of matches sorted by score
            min_interval: Minimum time interval between groups
            
        Returns:
            List of match groups
        """
        if not matches:
            return []
        
        # Sort matches by timestamp
        sorted_matches = sorted(matches, key=lambda x: x[2])
        
        groups = []
        current_group = [sorted_matches[0]]
        
        for match in sorted_matches[1:]:
            timestamp = match[2]
            last_timestamp = current_group[-1][2]
            
            if timestamp - last_timestamp < min_interval:
                # Add to current group
                current_group.append(match)
            else:
                # Start new group
                groups.append(current_group)
                current_group = [match]
        
        # Add the last group
        groups.append(current_group)
        
        return groups
    
    def create_highlight_reel(self, 
                            video_path: str, 
                            matches: List[Tuple[int, np.ndarray, float, float]],
                            output_filename: str = "highlight_reel.mp4",
                            clip_duration: float = 2.0,
                            max_clips: int = 10) -> Optional[str]:
        """
        Create a highlight reel from the best matching frames.
        
        Args:
            video_path: Path to the source video
            matches: List of matches
            output_filename: Name for the output highlight reel
            clip_duration: Duration of each clip segment
            max_clips: Maximum number of clips to include
            
        Returns:
            Path to the created highlight reel
        """
        if not matches:
            return None
        
        # Take the best matches
        best_matches = sorted(matches, key=lambda x: x[3], reverse=True)[:max_clips]
        
        # Group nearby matches
        grouped_matches = self._group_nearby_matches(best_matches, min_interval=5.0)
        
        if not grouped_matches:
            return None
        
        output_path = os.path.join(self.output_dir, output_filename)
        
        try:
            with VideoFileClip(video_path) as video:
                video_duration = video.duration
                clips = []
                
                for match_group in grouped_matches:
                    # Use the best match in the group
                    best_match = max(match_group, key=lambda x: x[3])
                    timestamp = best_match[2]
                    
                    # Calculate clip boundaries
                    half_duration = clip_duration / 2
                    start_time = max(0, timestamp - half_duration)
                    end_time = min(video_duration, timestamp + half_duration)
                    
                    # Create clip
                    clip = video.subclip(start_time, end_time)
                    clips.append(clip)
                
                if clips:
                    # Concatenate clips
                    try:
                        from moviepy.editor import concatenate_videoclips
                    except ImportError:
                        from moviepy.video.compositing.concatenate import concatenate_videoclips
                    final_clip = concatenate_videoclips(clips)
                    final_clip.write_videofile(output_path, codec="libx264", audio_codec="aac", verbose=False, logger=None)
                    
                    print(f"Created highlight reel: {output_path} ({len(clips)} clips)")
                    return output_path
        
        except Exception as e:
            print(f"Error creating highlight reel: {e}")
            return None
    
    def extract_frame_thumbnails(self,
                               matches: List[Tuple[int, np.ndarray, float, float]],
                               thumbnail_size: Tuple[int, int] = (480, 360),
                               image_quality: int = 90,
                               query: str = "",
                               extract_objects_only: bool = True) -> List[Tuple[str, float]]:
        """
        Save frame thumbnails from matches.

        Args:
            matches: List of matches
            thumbnail_size: Size for thumbnails (width, height)
            image_quality: JPEG quality (70-100)
            query: Search query for object extraction
            extract_objects_only: If True, extract only the searched objects

        Returns:
            List of (thumbnail_path, score) tuples
        """
        thumbnail_paths = []
        
        for i, (frame_idx, frame_array, timestamp, score) in enumerate(matches):
            minutes = int(timestamp // 60)
            seconds = int(timestamp % 60)

            if extract_objects_only and self.has_object_extraction and query:
                # Extract specific objects from the frame
                try:
                    extracted_objects = self.object_extractor.extract_objects_from_frame(
                        frame_array, query, confidence_threshold=0.3, max_objects=3
                    )

                    if extracted_objects:
                        # Save each extracted object
                        for obj_idx, (obj_img, obj_confidence, obj_class) in enumerate(extracted_objects):
                            # Resize object image
                            obj_resized = cv2.resize(obj_img, thumbnail_size)

                            # Convert RGB to BGR for OpenCV
                            obj_bgr = cv2.cvtColor(obj_resized, cv2.COLOR_RGB2BGR)

                            # Create filename for extracted object
                            obj_filename = f"object_{i+1:02d}_{obj_idx+1}_time_{minutes:02d}m{seconds:02d}s_score_{score:.3f}_{obj_class}.jpg"
                            obj_path = os.path.join(self.output_dir, obj_filename)

                            # Save extracted object
                            cv2.imwrite(obj_path, obj_bgr, [cv2.IMWRITE_JPEG_QUALITY, image_quality])
                            thumbnail_paths.append((obj_path, score))

                            print(f"Saved extracted object: {obj_filename} (Score: {score:.3f}, Object: {obj_class})")
                    else:
                        # Fallback to full frame if no objects extracted
                        self._save_full_frame(frame_array, thumbnail_size, image_quality, i, timestamp, score, thumbnail_paths)

                except Exception as e:
                    print(f"Error extracting objects: {e}")
                    # Fallback to full frame
                    self._save_full_frame(frame_array, thumbnail_size, image_quality, i, timestamp, score, thumbnail_paths)
            else:
                # Save full frame (original behavior)
                self._save_full_frame(frame_array, thumbnail_size, image_quality, i, timestamp, score, thumbnail_paths)
        
        return thumbnail_paths

    def _save_full_frame(self, frame_array: np.ndarray, thumbnail_size: Tuple[int, int],
                        image_quality: int, match_idx: int, timestamp: float,
                        score: float, thumbnail_paths: List[Tuple[str, float]]):
        """Helper method to save full frame as thumbnail."""
        # Resize frame
        frame_resized = cv2.resize(frame_array, thumbnail_size)

        # Convert RGB to BGR for OpenCV
        frame_bgr = cv2.cvtColor(frame_resized, cv2.COLOR_RGB2BGR)

        # Create descriptive filename with timestamp
        minutes = int(timestamp // 60)
        seconds = int(timestamp % 60)
        thumbnail_filename = f"frame_{match_idx+1:02d}_time_{minutes:02d}m{seconds:02d}s_score_{score:.3f}.jpg"
        thumbnail_path = os.path.join(self.output_dir, thumbnail_filename)

        # Save with specified quality
        cv2.imwrite(thumbnail_path, frame_bgr, [cv2.IMWRITE_JPEG_QUALITY, image_quality])
        thumbnail_paths.append((thumbnail_path, score))

        print(f"Saved frame: {thumbnail_filename} (Score: {score:.3f}, Time: {minutes:02d}:{seconds:02d})")

    def cleanup_output_dir(self):
        """Remove all files from the output directory."""
        if os.path.exists(self.output_dir):
            for filename in os.listdir(self.output_dir):
                file_path = os.path.join(self.output_dir, filename)
                try:
                    if os.path.isfile(file_path):
                        os.unlink(file_path)
                except Exception as e:
                    print(f"Error deleting {file_path}: {e}")


def create_video_clipper(output_dir: str = "static/output_clips") -> VideoClipper:
    """
    Factory function to create a video clipper.
    
    Args:
        output_dir: Directory to save output clips
        
    Returns:
        VideoClipper instance
    """
    return VideoClipper(output_dir=output_dir)
