# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
.venv/
.env/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Application specific
static/output_clips/*
!static/output_clips/.gitkeep
temp_videos/*
!temp_videos/.gitkeep
test_output/*
!test_output/.gitkeep

# Model cache
.cache/
models/cache/

# Logs
*.log
logs/

# Temporary files
*.tmp
*.temp

# Video files (examples)
*.mp4
*.avi
*.mov
*.mkv
*.wmv
*.flv
*.webm

# Image files (generated)
*.jpg
*.jpeg
*.png
*.gif
*.bmp

# Jupyter notebooks
.ipynb_checkpoints/
*.ipynb

# PyTorch
*.pth
*.pt

# Streamlit
.streamlit/
