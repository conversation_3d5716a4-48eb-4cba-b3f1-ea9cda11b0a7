"""
Enhanced Streamlit user interface for AI-powered video content search.
Features comprehensive video search, live detection, batch processing, and advanced settings.
"""

import streamlit as st
import os
import tempfile
import shutil
import json
import logging
import threading
from typing import Dict, Any, List, Optional
import time
import pandas as pd
from pathlib import Path
import plotly.express as px
import plotly.graph_objects as go

# Import our modules
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from utils.clip_match import VideoSearchEngine
    from utils.live_detection import StreamlitLiveDetection
    from config_advanced import AdvancedConfig
    from utils.advanced_matching import AdvancedMatcher
except ImportError:
    # Alternative import path
    sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from utils.clip_match import VideoSearchEngine
    from utils.live_detection import StreamlitLiveDetection
    try:
        from config_advanced import AdvancedConfig
        from utils.advanced_matching import AdvancedMatcher
    except ImportError:
        AdvancedConfig = None
        AdvancedMatcher = None

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def initialize_session_state():
    """Initialize comprehensive Streamlit session state variables."""
    # Core components
    if 'search_engine' not in st.session_state:
        st.session_state.search_engine = None
    if 'advanced_config' not in st.session_state:
        st.session_state.advanced_config = AdvancedConfig() if AdvancedConfig else None

    # Video file search
    if 'current_video_path' not in st.session_state:
        st.session_state.current_video_path = None
    if 'search_results' not in st.session_state:
        st.session_state.search_results = None
    if 'video_uploaded' not in st.session_state:
        st.session_state.video_uploaded = False
    if 'search_history' not in st.session_state:
        st.session_state.search_history = []

    # Batch processing
    if 'batch_videos' not in st.session_state:
        st.session_state.batch_videos = []
    if 'batch_results' not in st.session_state:
        st.session_state.batch_results = {}
    if 'batch_processing' not in st.session_state:
        st.session_state.batch_processing = False

    # Live detection
    if 'live_detector' not in st.session_state:
        st.session_state.live_detector = None
    if 'live_detection_active' not in st.session_state:
        st.session_state.live_detection_active = False

    # Performance tracking
    if 'performance_stats' not in st.session_state:
        st.session_state.performance_stats = {
            'total_searches': 0,
            'total_processing_time': 0,
            'average_processing_time': 0,
            'cache_hits': 0,
            'cache_misses': 0
        }

    # UI state
    if 'current_tab' not in st.session_state:
        st.session_state.current_tab = "Video File Search"
    if 'show_advanced_settings' not in st.session_state:
        st.session_state.show_advanced_settings = False


def create_search_engine(frame_interval=30, target_resolution=(512, 384), quality_factor=0.8,
                        enable_parallel_processing=True, max_workers=None):
    """Create and cache the enhanced search engine."""
    if st.session_state.search_engine is None:
        with st.spinner("🤖 Initializing AI models... This may take a moment."):
            try:
                # Get configuration
                config = st.session_state.advanced_config

                st.session_state.search_engine = VideoSearchEngine(
                    clip_model_name=config.models.clip_model_name if config else "openai/clip-vit-base-patch32",
                    output_dir=config.output.output_dir if config else "static/output_clips",
                    frame_interval=frame_interval,
                    target_resolution=target_resolution,
                    quality_factor=quality_factor,
                    use_chunked_processing=True,
                    chunk_size=config.processing.chunk_size if config else 200,
                    enable_parallel_processing=enable_parallel_processing,
                    max_workers=max_workers,
                    enable_memory_monitoring=config.memory.enable_memory_monitoring if config else True,
                    memory_limit_mb=config.memory.max_cache_size_mb if config else 2048
                )

                logger.info("Search engine initialized successfully")

            except Exception as e:
                st.error(f"Failed to initialize search engine: {e}")
                logger.error(f"Search engine initialization failed: {e}")
                return None

    return st.session_state.search_engine


def save_uploaded_video(uploaded_file) -> str:
    """Save uploaded video to temporary location."""
    # Create temp directory if it doesn't exist
    temp_dir = "temp_videos"
    os.makedirs(temp_dir, exist_ok=True)
    
    # Save uploaded file
    temp_path = os.path.join(temp_dir, uploaded_file.name)
    with open(temp_path, "wb") as f:
        f.write(uploaded_file.getbuffer())
    
    return temp_path


def display_video_info(video_info: Dict[str, Any]):
    """Display video information in a nice format."""
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("Duration", f"{video_info['duration_seconds']:.1f}s")
    with col2:
        st.metric("FPS", f"{video_info['fps']:.1f}")
    with col3:
        st.metric("Resolution", f"{video_info['width']}x{video_info['height']}")
    with col4:
        st.metric("Total Frames", f"{video_info['total_frames']:,}")


def display_search_results(results: Dict[str, Any]):
    """Display search results with clips and thumbnails."""
    if not results['matches']:
        st.warning("No matches found. Try adjusting the similarity threshold or using different search terms.")
        return
    
    st.success(f"Found {len(results['matches'])} matches!")
    
    # Display processing stats
    with st.expander("Search Statistics"):
        stats = results['stats']
        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("Processing Time", f"{results['processing_time']:.2f}s")
        with col2:
            st.metric("Frames Analyzed", f"{stats['total_frames_extracted']:,}")
        with col3:
            st.metric("Similarity Threshold", f"{stats['similarity_threshold']:.2f}")
    
    # Display results
    st.subheader("Search Results")
    
    # Create tabs for different result types
    if results['clips'] and results['thumbnails']:
        tab1, tab2, tab3 = st.tabs(["🖼️ Extracted Images", "📋 Match Details", "🎬 Video Clips"])
    elif results['thumbnails']:
        tab1, tab2 = st.tabs(["🖼️ Extracted Images", "📋 Match Details"])
    else:
        tab1, tab2 = st.tabs(["📋 Match Details", "🎬 Video Clips"])
    
    with tab1:
        # Check if this is the images tab or details tab
        if results['thumbnails'] and (not results['clips'] or len(results['thumbnails']) > 0):
            # This is the images tab
            st.write(f"🎯 Found {len(results['thumbnails'])} matching images:")

            # Display images in a responsive grid
            cols = st.columns(3)
            for i, thumbnail in enumerate(results['thumbnails']):
                with cols[i % 3]:
                    if os.path.exists(thumbnail['path']):
                        # Get match info for this thumbnail
                        match_info = results['matches'][i] if i < len(results['matches']) else None

                        # Display image with enhanced caption
                        st.image(thumbnail['path'], use_column_width=True)

                        if match_info:
                            st.caption(f"**Match {i+1}** | Time: {match_info['time_formatted']} | Score: {thumbnail['score']:.3f}")
                        else:
                            st.caption(f"**Match {i+1}** | Score: {thumbnail['score']:.3f}")

                        # Add download button for each image
                        with open(thumbnail['path'], "rb") as file:
                            btn = st.download_button(
                                label=f"📥 Download",
                                data=file.read(),
                                file_name=os.path.basename(thumbnail['path']),
                                mime="image/jpeg",
                                key=f"download_{i}",
                                use_container_width=True
                            )
                    else:
                        st.error(f"Image not found: {thumbnail['path']}")
        else:
            # This is the match details tab
            st.write("📋 **Detailed Match Information:**")

            for i, match in enumerate(results['matches']):
                with st.expander(f"Match {i+1} - {match['time_formatted']} (Score: {match['similarity_score']:.3f})"):
                    col1, col2, col3 = st.columns(3)
                    with col1:
                        st.metric("Timestamp", f"{match['timestamp']:.2f}s")
                    with col2:
                        st.metric("Similarity Score", f"{match['similarity_score']:.3f}")
                    with col3:
                        st.metric("Frame Number", match['frame_index'])

                    # Show thumbnail if available
                    if i < len(results['thumbnails']) and os.path.exists(results['thumbnails'][i]['path']):
                        st.image(results['thumbnails'][i]['path'], width=200, caption="Preview")
    
    with tab2:
        # Check if this is match details tab or clips tab
        if results['thumbnails'] and len(results['thumbnails']) > 0:
            # This is the match details tab (when images are shown in tab1)
            st.write("📋 **Detailed Match Information:**")

            for i, match in enumerate(results['matches']):
                with st.expander(f"Match {i+1} - {match['time_formatted']} (Score: {match['similarity_score']:.3f})"):
                    col1, col2, col3 = st.columns(3)
                    with col1:
                        st.metric("Timestamp", f"{match['timestamp']:.2f}s")
                    with col2:
                        st.metric("Similarity Score", f"{match['similarity_score']:.3f}")
                    with col3:
                        st.metric("Frame Number", match['frame_index'])

                    # Show thumbnail if available
                    if i < len(results['thumbnails']) and os.path.exists(results['thumbnails'][i]['path']):
                        st.image(results['thumbnails'][i]['path'], width=200, caption="Preview")
        else:
            # This is the clips tab (when no images or images in tab3)
            if results['clips']:
                st.write(f"🎬 Generated {len(results['clips'])} video clips:")

                # Display clips in a grid
                cols = st.columns(2)
                for i, clip_path in enumerate(results['clips']):
                    with cols[i % 2]:
                        if os.path.exists(clip_path):
                            st.video(clip_path)
                            st.caption(f"Clip {i+1}: {os.path.basename(clip_path)}")
                        else:
                            st.error(f"Clip file not found: {clip_path}")
            else:
                st.info("No video clips were generated. Enable 'Create Video Clips' in the settings.")
    
    # Handle tab3 only if it exists (when both clips and thumbnails are available)
    if results['clips'] and results['thumbnails']:
        with tab3:
            # This is the clips tab
            if results['clips']:
                st.write(f"🎬 Generated {len(results['clips'])} video clips:")

                # Display clips in a grid
                cols = st.columns(2)
                for i, clip_path in enumerate(results['clips']):
                    with cols[i % 2]:
                        if os.path.exists(clip_path):
                            st.video(clip_path)
                            st.caption(f"Clip {i+1}: {os.path.basename(clip_path)}")
                        else:
                            st.error(f"Clip file not found: {clip_path}")
            else:
                st.info("No video clips were generated. Enable 'Create Video Clips' in the settings.")


def main():
    """Enhanced main Streamlit application with comprehensive features."""
    st.set_page_config(
        page_title="AI Video Content Search Pro",
        page_icon="🎬",
        layout="wide",
        initial_sidebar_state="expanded",
        menu_items={
            'Get Help': 'https://github.com/yourusername/ai-video-search',
            'Report a bug': 'https://github.com/yourusername/ai-video-search/issues',
            'About': "AI-Powered Video Content Search - Professional Edition"
        }
    )

    # Initialize session state
    initialize_session_state()

    # Custom CSS for better styling
    st.markdown("""
    <style>
    .main-header {
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        padding: 1rem;
        border-radius: 10px;
        margin-bottom: 2rem;
    }
    .metric-card {
        background: #f8f9fa;
        padding: 1rem;
        border-radius: 8px;
        border-left: 4px solid #667eea;
    }
    .success-message {
        background: #d4edda;
        color: #155724;
        padding: 0.75rem;
        border-radius: 5px;
        border: 1px solid #c3e6cb;
    }
    .warning-message {
        background: #fff3cd;
        color: #856404;
        padding: 0.75rem;
        border-radius: 5px;
        border: 1px solid #ffeaa7;
    }
    </style>
    """, unsafe_allow_html=True)

    # Header with gradient background
    st.markdown("""
    <div class="main-header">
        <h1 style="color: white; margin: 0;">🎬 AI-Powered Video Content Search Pro</h1>
        <p style="color: #e0e0e0; margin: 0;">Advanced video analysis with CLIP, YOLOv8, and real-time detection</p>
    </div>
    """, unsafe_allow_html=True)

    # Performance metrics in header
    render_performance_metrics()

    # Main navigation tabs with enhanced features
    tab1, tab2, tab3, tab4, tab5 = st.tabs([
        "📁 Video File Search",
        "📹 Live Video Detection",
        "📊 Batch Processing",
        "📈 Analytics & History",
        "⚙️ Advanced Settings"
    ])

    with tab1:
        render_video_file_interface()

    with tab2:
        render_live_detection_interface()

    with tab3:
        render_batch_processing_interface()

    with tab4:
        render_analytics_interface()

    with tab5:
        render_advanced_settings_interface()


def render_video_file_interface():
    """Render the video file search interface."""
    
def render_live_detection_interface():
    """Render the live video detection interface."""
    live_detection = StreamlitLiveDetection()
    live_detection.render_live_detection_interface()


def render_video_file_interface():
    """Render the video file search interface."""
    # Sidebar for settings
    with st.sidebar:
        st.header("⚙️ Settings")
        
        # Search settings
        st.subheader("🎯 Search Accuracy")

        use_advanced_matching = st.checkbox(
            "Advanced Matching",
            value=True,
            help="Use improved AI techniques for better accuracy and fewer false positives"
        )

        if use_advanced_matching:
            similarity_threshold = st.slider(
                "Similarity Threshold",
                min_value=0.15,
                max_value=0.4,
                value=0.25,
                step=0.05,
                help="Higher thresholds with advanced matching give better results"
            )
            st.info("💡 Advanced matching enabled: You can use higher thresholds (0.25-0.3) for better accuracy!")
        else:
            similarity_threshold = st.slider(
                "Similarity Threshold",
                min_value=0.1,
                max_value=0.5,
                value=0.2,
                step=0.05,
                help="Lower values return more results but may be less accurate"
            )
            st.warning("⚠️ Basic matching: Lower thresholds may give false positives")

        max_results = st.number_input(
            "Max Results",
            min_value=1,
            max_value=100,
            value=20,
            help="Maximum number of results to return"
        )

        # Large video settings
        st.subheader("Large Video Settings")
        frame_interval = st.slider(
            "Frame Interval",
            min_value=15,
            max_value=120,
            value=30,
            step=15,
            help="Extract every N frames. Higher values = faster processing, lower accuracy"
        )

        target_resolution = st.selectbox(
            "Processing Resolution",
            options=[
                ("High Quality (720x540)", (720, 540)),
                ("Medium Quality (512x384)", (512, 384)),
                ("Low Quality (320x240)", (320, 240)),
                ("Original Resolution", None)
            ],
            index=1,
            help="Lower resolution = faster processing and less memory usage"
        )

        quality_factor = st.slider(
            "Compression Quality",
            min_value=0.3,
            max_value=1.0,
            value=0.8,
            step=0.1,
            help="Lower values = more compression, less memory usage"
        )
        
        # Output settings
        st.subheader("🖼️ Image Extraction Options")
        create_thumbnails = st.checkbox("Extract Images", value=True, help="Extract still images from matching frames (faster)")

        if create_thumbnails:
            extract_objects_only = st.checkbox(
                "Extract Objects Only",
                value=True,
                help="🎯 Extract only the searched object (e.g., just the car, not the whole frame)"
            )

            if extract_objects_only:
                st.info("💡 This will show only the specific object you're searching for, not the entire video frame!")
        else:
            extract_objects_only = False

        create_clips = st.checkbox("Create Video Clips", value=False, help="Generate short video clips (slower)")

        # Image settings
        if create_thumbnails:
            thumbnail_size = st.selectbox(
                "Image Size",
                options=[
                    ("Large (640x480)", (640, 480)),
                    ("Medium (480x360)", (480, 360)),
                    ("Small (320x240)", (320, 240)),
                    ("Thumbnail (160x120)", (160, 120))
                ],
                index=1,
                help="Size of extracted images"
            )

            image_quality = st.slider(
                "Image Quality",
                min_value=70,
                max_value=100,
                value=90,
                step=5,
                help="JPEG quality for saved images"
            )
        else:
            thumbnail_size = (480, 360)
            image_quality = 90

        if create_clips:
            clip_duration = st.slider(
                "Clip Duration (seconds)",
                min_value=1.0,
                max_value=10.0,
                value=3.0,
                step=0.5
            )
        else:
            clip_duration = 3.0
        
        # Cache management
        st.subheader("Memory & Cache")

        # Show cache info
        if st.session_state.search_engine:
            cache_info = st.session_state.search_engine.get_cache_info()
            col1, col2 = st.columns(2)
            with col1:
                st.metric("Cached Videos", len(cache_info['cached_videos']))
                st.metric("Memory Usage", f"{cache_info['memory_usage_mb']:.1f} MB")
            with col2:
                st.metric("Cached Frames", cache_info['total_cached_frames'])
                st.metric("Memory Limit", f"{cache_info['memory_limit_mb']} MB")

        if st.button("Clear Cache"):
            if st.session_state.search_engine:
                st.session_state.search_engine.clear_cache()
                st.success("Cache cleared!")

        if st.button("Clean Output Directory"):
            if st.session_state.search_engine:
                st.session_state.search_engine.cleanup_outputs()
                st.success("Output directory cleaned!")
    
    # Main content area
    col1, col2 = st.columns([1, 2])
    
    with col1:
        st.header("📁 Upload Video")
        
        # Video upload
        uploaded_file = st.file_uploader(
            "Choose a video file",
            type=['mp4', 'avi', 'mov', 'mkv', 'wmv'],
            help="Supported formats: MP4, AVI, MOV, MKV, WMV"
        )
        
        if uploaded_file is not None:
            # Save uploaded video
            if not st.session_state.video_uploaded or st.session_state.current_video_path != uploaded_file.name:
                with st.spinner("Uploading video..."):
                    video_path = save_uploaded_video(uploaded_file)
                    st.session_state.current_video_path = video_path
                    st.session_state.video_uploaded = True
                    st.session_state.search_results = None  # Clear previous results
                
                st.success("Video uploaded successfully!")
            
            # Display video
            st.video(st.session_state.current_video_path)
            
            # Create search engine with current settings
            search_engine = create_search_engine(
                frame_interval=frame_interval,
                target_resolution=target_resolution[1],
                quality_factor=quality_factor
            )
            
            # Get and display video info
            try:
                video_info = search_engine._get_video_info(st.session_state.current_video_path)
                st.subheader("📊 Video Information")
                display_video_info(video_info)
            except Exception as e:
                st.error(f"Error reading video: {e}")
    
    with col2:
        st.header("🔍 Search")
        
        if st.session_state.video_uploaded:
            # Search interface
            query = st.text_input(
                "What are you looking for?",
                placeholder="e.g., 'red cap', 'person walking', 'car driving', 'sunset'",
                help="Describe what you want to find in the video using natural language"
            )
            
            # Search button
            if st.button("🔍 Search Video", type="primary", disabled=not query):
                if query:
                    search_engine = create_search_engine(
                        frame_interval=frame_interval,
                        target_resolution=target_resolution[1],
                        quality_factor=quality_factor
                    )

                    if search_engine:
                        # Progress tracking
                        progress_bar = st.progress(0)
                        status_text = st.empty()

                        def progress_callback(message, progress):
                            progress_bar.progress(progress)
                            status_text.text(message)

                        try:
                            start_time = time.time()

                            results = search_engine.search_video(
                                video_path=st.session_state.current_video_path,
                                query=query,
                                similarity_threshold=similarity_threshold,
                                top_k=max_results,
                                create_clips=create_clips,
                                clip_duration=clip_duration,
                                create_thumbnails=create_thumbnails,
                                thumbnail_size=thumbnail_size[1],
                                image_quality=image_quality,
                                use_advanced_matching=use_advanced_matching,
                                progress_callback=progress_callback
                            )

                            # Clear progress indicators
                            progress_bar.empty()
                            status_text.empty()

                            st.session_state.search_results = results

                            # Add to search history
                            search_record = {
                                'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
                                'query': query,
                                'video_name': os.path.basename(st.session_state.current_video_path),
                                'matches_found': len(results['matches']),
                                'processing_time': results['processing_time'],
                                'similarity_threshold': similarity_threshold,
                                'advanced_matching': use_advanced_matching
                            }
                            st.session_state.search_history.append(search_record)

                            # Update performance stats
                            if 'stats' in results:
                                st.session_state.performance_stats['total_searches'] += 1
                                st.session_state.performance_stats['total_processing_time'] += results['processing_time']
                                st.session_state.performance_stats['average_processing_time'] = (
                                    st.session_state.performance_stats['total_processing_time'] /
                                    st.session_state.performance_stats['total_searches']
                                )

                        except Exception as e:
                            progress_bar.empty()
                            status_text.empty()
                            st.error(f"❌ Search failed: {e}")
                            st.error("💡 Try reducing frame interval or processing resolution for large videos")
                            logger.error(f"Search failed: {e}")
                            st.session_state.search_results = None
            
            # Display search results
            if st.session_state.search_results:
                st.divider()
                display_search_results(st.session_state.search_results)
            
            # Example queries
            st.subheader("💡 Example Queries")
            example_queries = [
                "person wearing red",
                "car on the road",
                "dog running",
                "sunset or sunrise",
                "people talking",
                "green trees",
                "building or house",
                "water or ocean"
            ]
            
            cols = st.columns(2)
            for i, example in enumerate(example_queries):
                with cols[i % 2]:
                    if st.button(f"'{example}'", key=f"example_{i}"):
                        st.rerun()
        
        else:
            st.info("👆 Please upload a video file to start searching!")


def render_performance_metrics():
    """Render performance metrics in the header."""
    if st.session_state.search_engine:
        try:
            stats = st.session_state.search_engine.get_performance_stats()
            cache_info = st.session_state.search_engine.get_cache_info()

            col1, col2, col3, col4, col5 = st.columns(5)

            with col1:
                st.metric(
                    "Total Searches",
                    stats['total_searches'],
                    help="Total number of searches performed"
                )

            with col2:
                st.metric(
                    "Avg Processing Time",
                    f"{stats['average_processing_time']:.1f}s",
                    help="Average time per search"
                )

            with col3:
                st.metric(
                    "Cache Hit Rate",
                    f"{stats['cache_hit_rate']:.1%}",
                    help="Percentage of cache hits vs misses"
                )

            with col4:
                st.metric(
                    "Memory Usage",
                    f"{cache_info['memory_usage_mb']:.1f}MB",
                    help="Current memory usage"
                )

            with col5:
                st.metric(
                    "Cached Videos",
                    len(cache_info['cached_videos']),
                    help="Number of videos in cache"
                )

        except Exception as e:
            logger.warning(f"Failed to get performance metrics: {e}")


def render_batch_processing_interface():
    """Render the batch processing interface."""
    st.header("📊 Batch Video Processing")
    st.markdown("Process multiple videos with the same query or multiple queries on one video.")

    # Batch mode selection
    batch_mode = st.radio(
        "Batch Processing Mode",
        ["Multiple Videos, One Query", "One Video, Multiple Queries"],
        help="Choose how you want to process videos in batch"
    )

    if batch_mode == "Multiple Videos, One Query":
        render_multi_video_batch()
    else:
        render_multi_query_batch()


def render_multi_video_batch():
    """Render interface for processing multiple videos with one query."""
    st.subheader("Multiple Videos, Single Query")

    # File uploader for multiple videos
    uploaded_files = st.file_uploader(
        "Upload multiple video files",
        type=['mp4', 'avi', 'mov', 'mkv', 'wmv'],
        accept_multiple_files=True,
        help="Select multiple video files to process with the same query"
    )

    if uploaded_files:
        st.success(f"Uploaded {len(uploaded_files)} video files")

        # Display uploaded files
        for i, file in enumerate(uploaded_files):
            st.write(f"{i+1}. {file.name} ({file.size / (1024*1024):.1f} MB)")

        # Query input
        query = st.text_input(
            "Search Query",
            placeholder="e.g., 'red car', 'person walking', 'sunset'",
            help="This query will be applied to all uploaded videos"
        )

        # Processing settings
        col1, col2 = st.columns(2)
        with col1:
            similarity_threshold = st.slider("Similarity Threshold", 0.1, 0.5, 0.2, 0.05)
            max_results = st.number_input("Max Results per Video", 1, 50, 10)

        with col2:
            create_clips = st.checkbox("Create Video Clips", value=False)
            create_thumbnails = st.checkbox("Create Thumbnails", value=True)

        # Process button
        if st.button("🚀 Process All Videos", type="primary", disabled=not query):
            process_batch_videos(uploaded_files, query, similarity_threshold,
                                max_results, create_clips, create_thumbnails)


def render_multi_query_batch():
    """Render interface for processing one video with multiple queries."""
    st.subheader("Single Video, Multiple Queries")

    # Single video upload
    uploaded_file = st.file_uploader(
        "Upload video file",
        type=['mp4', 'avi', 'mov', 'mkv', 'wmv'],
        help="Select one video file to process with multiple queries"
    )

    if uploaded_file:
        st.success(f"Uploaded: {uploaded_file.name}")

        # Multiple queries input
        st.subheader("Search Queries")

        # Initialize queries in session state
        if 'batch_queries' not in st.session_state:
            st.session_state.batch_queries = [""]

        # Dynamic query inputs
        for i, query in enumerate(st.session_state.batch_queries):
            col1, col2 = st.columns([4, 1])
            with col1:
                new_query = st.text_input(
                    f"Query {i+1}",
                    value=query,
                    key=f"query_{i}",
                    placeholder=f"e.g., 'red car', 'person walking'"
                )
                st.session_state.batch_queries[i] = new_query

            with col2:
                if st.button("❌", key=f"remove_{i}", help="Remove this query"):
                    if len(st.session_state.batch_queries) > 1:
                        st.session_state.batch_queries.pop(i)
                        st.experimental_rerun()

        # Add query button
        if st.button("➕ Add Query"):
            st.session_state.batch_queries.append("")
            st.experimental_rerun()

        # Processing settings
        col1, col2 = st.columns(2)
        with col1:
            similarity_threshold = st.slider("Similarity Threshold", 0.1, 0.5, 0.2, 0.05)
            max_results = st.number_input("Max Results per Query", 1, 50, 10)

        with col2:
            create_clips = st.checkbox("Create Video Clips", value=False)
            create_thumbnails = st.checkbox("Create Thumbnails", value=True)

        # Process button
        valid_queries = [q for q in st.session_state.batch_queries if q.strip()]
        if st.button("🚀 Process All Queries", type="primary", disabled=not valid_queries):
            process_batch_queries(uploaded_file, valid_queries, similarity_threshold,
                                max_results, create_clips, create_thumbnails)


def render_analytics_interface():
    """Render the analytics and history interface."""
    st.header("📈 Analytics & Search History")

    # Search history
    if st.session_state.search_history:
        st.subheader("Recent Searches")

        # Convert to DataFrame for better display
        df = pd.DataFrame(st.session_state.search_history)

        # Display as table
        st.dataframe(
            df[['timestamp', 'query', 'video_name', 'matches_found', 'processing_time']],
            use_container_width=True
        )

        # Analytics charts
        if len(df) > 1:
            st.subheader("Search Analytics")

            col1, col2 = st.columns(2)

            with col1:
                # Processing time trend
                fig_time = px.line(
                    df,
                    x='timestamp',
                    y='processing_time',
                    title="Processing Time Trend",
                    labels={'processing_time': 'Time (seconds)'}
                )
                st.plotly_chart(fig_time, use_container_width=True)

            with col2:
                # Matches found distribution
                fig_matches = px.histogram(
                    df,
                    x='matches_found',
                    title="Matches Found Distribution",
                    labels={'matches_found': 'Number of Matches'}
                )
                st.plotly_chart(fig_matches, use_container_width=True)

        # Clear history button
        if st.button("🗑️ Clear Search History"):
            st.session_state.search_history = []
            st.success("Search history cleared!")
            st.experimental_rerun()

    else:
        st.info("No search history available. Perform some searches to see analytics here.")


def render_advanced_settings_interface():
    """Render the advanced settings interface."""
    st.header("⚙️ Advanced Settings")

    if st.session_state.advanced_config:
        config = st.session_state.advanced_config

        # Model settings
        with st.expander("🤖 Model Settings", expanded=False):
            col1, col2 = st.columns(2)

            with col1:
                new_clip_model = st.selectbox(
                    "CLIP Model",
                    ["openai/clip-vit-base-patch32", "openai/clip-vit-large-patch14"],
                    index=0 if config.models.clip_model_name == "openai/clip-vit-base-patch32" else 1
                )

                new_device = st.selectbox(
                    "Device",
                    ["auto", "cpu", "cuda"],
                    index=["auto", "cpu", "cuda"].index(config.models.device)
                )

            with col2:
                new_yolo_model = st.selectbox(
                    "YOLO Model",
                    ["yolov8n.pt", "yolov8s.pt", "yolov8m.pt"],
                    index=0
                )

                enable_optimization = st.checkbox(
                    "Enable Model Optimization",
                    value=config.models.enable_model_optimization
                )

        # Performance settings
        with st.expander("⚡ Performance Settings", expanded=False):
            col1, col2 = st.columns(2)

            with col1:
                enable_gpu = st.checkbox(
                    "Enable GPU Acceleration",
                    value=config.performance.enable_gpu
                )

                batch_size = st.slider(
                    "Batch Size",
                    1, 64, config.performance.batch_size
                )

            with col2:
                num_workers = st.slider(
                    "Number of Workers",
                    1, 8, config.performance.num_workers
                )

                enable_mixed_precision = st.checkbox(
                    "Enable Mixed Precision",
                    value=config.performance.enable_mixed_precision
                )

        # Memory settings
        with st.expander("💾 Memory Management", expanded=False):
            col1, col2 = st.columns(2)

            with col1:
                max_cache_size = st.slider(
                    "Max Cache Size (MB)",
                    512, 8192, config.memory.max_cache_size_mb
                )

                enable_monitoring = st.checkbox(
                    "Enable Memory Monitoring",
                    value=config.memory.enable_memory_monitoring
                )

            with col2:
                cleanup_threshold = st.slider(
                    "Auto Cleanup Threshold",
                    0.5, 0.95, config.memory.auto_cleanup_threshold
                )

                frame_cache_limit = st.number_input(
                    "Frame Cache Limit",
                    100, 5000, config.memory.frame_cache_limit
                )

        # Save settings button
        if st.button("💾 Save Settings"):
            # Update configuration
            config.models.clip_model_name = new_clip_model
            config.models.device = new_device
            config.models.yolo_model_name = new_yolo_model
            config.models.enable_model_optimization = enable_optimization

            config.performance.enable_gpu = enable_gpu
            config.performance.batch_size = batch_size
            config.performance.num_workers = num_workers
            config.performance.enable_mixed_precision = enable_mixed_precision

            config.memory.max_cache_size_mb = max_cache_size
            config.memory.enable_memory_monitoring = enable_monitoring
            config.memory.auto_cleanup_threshold = cleanup_threshold
            config.memory.frame_cache_limit = frame_cache_limit

            # Save to file
            if config.save_config():
                st.success("Settings saved successfully!")
                # Reset search engine to apply new settings
                st.session_state.search_engine = None
            else:
                st.error("Failed to save settings")

    else:
        st.warning("Advanced configuration not available")


def process_batch_videos(uploaded_files, query, similarity_threshold,
                        max_results, create_clips, create_thumbnails):
    """Process multiple videos with a single query."""
    st.session_state.batch_processing = True

    # Create search engine
    search_engine = create_search_engine()
    if not search_engine:
        st.error("Failed to initialize search engine")
        return

    # Progress tracking
    progress_bar = st.progress(0)
    status_text = st.empty()
    results_container = st.container()

    batch_results = {}

    for i, uploaded_file in enumerate(uploaded_files):
        try:
            # Update progress
            progress = (i + 1) / len(uploaded_files)
            progress_bar.progress(progress)
            status_text.text(f"Processing {uploaded_file.name}... ({i+1}/{len(uploaded_files)})")

            # Save uploaded file
            video_path = save_uploaded_video(uploaded_file)

            # Search video
            results = search_engine.search_video(
                video_path=video_path,
                query=query,
                similarity_threshold=similarity_threshold,
                top_k=max_results,
                create_clips=create_clips,
                create_thumbnails=create_thumbnails
            )

            batch_results[uploaded_file.name] = results

            # Display results
            with results_container:
                st.subheader(f"Results for {uploaded_file.name}")
                if results['matches']:
                    st.success(f"Found {len(results['matches'])} matches")
                    display_search_results(results)
                else:
                    st.warning("No matches found")

                st.divider()

        except Exception as e:
            st.error(f"Error processing {uploaded_file.name}: {e}")
            logger.error(f"Batch processing error for {uploaded_file.name}: {e}")

    # Final status
    status_text.text("Batch processing completed!")
    st.session_state.batch_results = batch_results
    st.session_state.batch_processing = False

    # Summary
    total_matches = sum(len(result['matches']) for result in batch_results.values())
    st.success(f"Batch processing completed! Total matches found: {total_matches}")


def process_batch_queries(uploaded_file, queries, similarity_threshold,
                         max_results, create_clips, create_thumbnails):
    """Process one video with multiple queries."""
    st.session_state.batch_processing = True

    # Create search engine
    search_engine = create_search_engine()
    if not search_engine:
        st.error("Failed to initialize search engine")
        return

    # Save uploaded file
    video_path = save_uploaded_video(uploaded_file)

    # Progress tracking
    progress_bar = st.progress(0)
    status_text = st.empty()
    results_container = st.container()

    batch_results = {}

    for i, query in enumerate(queries):
        try:
            # Update progress
            progress = (i + 1) / len(queries)
            progress_bar.progress(progress)
            status_text.text(f"Processing query: '{query}'... ({i+1}/{len(queries)})")

            # Search video
            results = search_engine.search_video(
                video_path=video_path,
                query=query,
                similarity_threshold=similarity_threshold,
                top_k=max_results,
                create_clips=create_clips,
                create_thumbnails=create_thumbnails
            )

            batch_results[query] = results

            # Display results
            with results_container:
                st.subheader(f"Results for '{query}'")
                if results['matches']:
                    st.success(f"Found {len(results['matches'])} matches")
                    display_search_results(results)
                else:
                    st.warning("No matches found")

                st.divider()

        except Exception as e:
            st.error(f"Error processing query '{query}': {e}")
            logger.error(f"Batch processing error for query '{query}': {e}")

    # Final status
    status_text.text("Batch processing completed!")
    st.session_state.batch_results = batch_results
    st.session_state.batch_processing = False

    # Summary
    total_matches = sum(len(result['matches']) for result in batch_results.values())
    st.success(f"Batch processing completed! Total matches found: {total_matches}")


if __name__ == "__main__":
    main()
