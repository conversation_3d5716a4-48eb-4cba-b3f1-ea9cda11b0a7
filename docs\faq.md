# ❓ Frequently Asked Questions (FAQ)

## 🚀 Getting Started

### **Q: What is AI-Powered Video Content Search?**
A: It's an intelligent video search system that uses AI models (CLIP and YOLOv8) to find specific objects, people, or scenes in videos using natural language queries. You can search for "red car", "person walking", or "sunset scene" and get timestamped results.

### **Q: What makes this different from regular video search?**
A: Unlike keyword-based search that relies on metadata, our system analyzes the actual visual content of videos using advanced AI models. It understands what's happening in each frame and matches it to your natural language descriptions.

### **Q: Do I need technical expertise to use this?**
A: No! The web interface is designed for non-technical users. Simply upload a video, type what you're looking for, and click search. For advanced users, we also provide command-line tools and APIs.

## 💻 Installation & Setup

### **Q: What are the system requirements?**
A: **Minimum:** Python 3.8+, 4GB RAM, 10GB disk space. **Recommended:** Python 3.10+, 8GB+ RAM, GPU with 4GB+ VRAM, SSD storage. See [System Requirements](system-requirements.md) for details.

### **Q: Do I need a GPU?**
A: No, but it's highly recommended. The system works on CPU but is 3-10x faster with GPU acceleration. We support NVIDIA CUDA, AMD ROCm, and Apple Silicon MPS.

### **Q: How long does installation take?**
A: 5-15 minutes with automated installers, depending on your internet speed. Docker installation takes 10-20 minutes including image build.

### **Q: Can I install this on Windows/Mac/Linux?**
A: Yes! We provide automated installers for all major platforms:
- **Windows:** `install.bat`
- **Linux/macOS:** `install.sh`
- **Docker:** `docker-install.sh` (universal)

### **Q: I'm getting installation errors. What should I do?**
A: Run our diagnostic tool: `python test_installation.py`. Check the [Troubleshooting Guide](troubleshooting.md) for common solutions, or create an issue on GitHub with the diagnostic output.

## 🎬 Video Processing

### **Q: What video formats are supported?**
A: MP4, AVI, MOV, MKV, WMV, FLV, WebM. Most common video formats are supported through FFmpeg.

### **Q: What's the maximum video size I can process?**
A: There's no hard limit, but processing time increases with video size. We've tested with 10GB+ videos using chunked processing. For very large videos, consider increasing frame intervals or using lower resolution.

### **Q: How long does video processing take?**
A: Depends on video length, resolution, and hardware:
- **Small videos (< 1GB):** 30 seconds - 5 minutes
- **Medium videos (1-5GB):** 2-15 minutes  
- **Large videos (> 5GB):** 10+ minutes
- **GPU acceleration** provides 3-10x speedup

### **Q: Can I process multiple videos at once?**
A: Yes! Use batch processing in the web interface or command line:
```bash
python main.py --batch-videos video1.mp4 video2.mp4 --query "car"
```

### **Q: Why are my search results inaccurate?**
A: Try these approaches:
- **Be more specific:** "red sports car" vs "car"
- **Adjust similarity threshold:** Lower for more results, higher for accuracy
- **Use different query variations:** "vehicle", "automobile", "sedan"
- **Check video quality:** Low-quality videos may produce poor results

## 🔍 Search Queries

### **Q: How should I write search queries?**
A: **Good examples:**
- "red car" (object + attribute)
- "person wearing glasses" (person + characteristic)
- "sunset over water" (scene description)
- "dog running in park" (action + context)

**Avoid:** Very generic terms like "thing", "stuff", or overly complex descriptions.

### **Q: What languages are supported for queries?**
A: Currently English only. The CLIP model was primarily trained on English descriptions, though some other languages may work with limited accuracy.

### **Q: Can I search for multiple objects at once?**
A: Use the batch processing feature with multiple queries, or try compound descriptions like "person and dog" or "car and building".

### **Q: Why do I get no results for my query?**
A: Common causes:
- **Similarity threshold too high:** Try lowering to 0.1-0.15
- **Object not in video:** Verify the content exists
- **Query too specific:** Try broader terms first
- **Video quality issues:** Low resolution or poor lighting

## 📹 Live Detection

### **Q: Can I use this with my security cameras?**
A: Yes! We support:
- **USB/Webcams:** Direct camera access
- **IP Cameras:** RTSP stream support
- **Security Systems:** Most RTSP-compatible systems

### **Q: What's the difference between live detection and video search?**
A: **Live detection** processes video streams in real-time, while **video search** analyzes pre-recorded files. Live detection is optimized for speed and continuous monitoring.

### **Q: How do I find my camera's RTSP URL?**
A: Common formats:
- `rtsp://ip:554/stream`
- `rtsp://username:password@ip:port/live/ch1`
- Check your camera manual or manufacturer's documentation

### **Q: Can I record live detection results?**
A: Yes, detected objects are automatically saved as images. You can configure the maximum number of detections to keep and enable automatic saving.

## ⚙️ Configuration & Customization

### **Q: How do I improve processing speed?**
A: **Speed optimizations:**
- Enable GPU acceleration
- Increase frame interval (sample fewer frames)
- Use lower processing resolution
- Use smaller CLIP models
- Disable video clip generation (images only)

### **Q: How do I improve search accuracy?**
A: **Accuracy improvements:**
- Decrease frame interval (sample more frames)
- Use larger CLIP models
- Enable advanced matching
- Use higher similarity thresholds
- Ensure good video quality

### **Q: Can I use different AI models?**
A: Yes! Configure in `config.json`:
```json
{
  "models": {
    "clip_model_name": "openai/clip-vit-large-patch14",
    "yolo_model_name": "yolov8x.pt"
  }
}
```

### **Q: How do I configure memory usage?**
A: Edit `config.json`:
```json
{
  "memory": {
    "max_cache_size_mb": 4096,
    "enable_memory_monitoring": true,
    "auto_cleanup_threshold": 0.8
  }
}
```

## 🐳 Docker & Deployment

### **Q: Should I use Docker or native installation?**
A: **Docker advantages:** Consistent environment, easy deployment, includes optional services (Redis, monitoring). **Native advantages:** Better performance, easier development, direct system access.

### **Q: Can I deploy this in production?**
A: Yes! We provide production-ready Docker configurations with:
- Load balancing (Nginx)
- Caching (Redis)
- Monitoring (Prometheus/Grafana)
- Database storage (PostgreSQL)

### **Q: How do I scale for multiple users?**
A: Use Docker Compose with multiple application instances behind Nginx load balancer. Configure Redis for shared caching and PostgreSQL for metadata storage.

### **Q: Can I deploy this in the cloud?**
A: Yes! Works on AWS, Azure, GCP, and other cloud platforms. We provide cloud-optimized requirements and configuration examples.

## 🔧 Technical Questions

### **Q: What AI models does this use?**
A: **Primary models:**
- **CLIP (OpenAI):** Text-image similarity matching
- **YOLOv8 (Ultralytics):** Object detection and localization

**Model variants:** Different sizes available for speed vs accuracy trade-offs.

### **Q: How does the similarity scoring work?**
A: CLIP generates embeddings for both text queries and video frames, then calculates cosine similarity. Scores range from 0-1, where higher scores indicate better matches.

### **Q: Can I integrate this with other applications?**
A: Yes! We provide:
- **Python API:** Direct integration with Python applications
- **REST API:** HTTP endpoints for web services
- **Command line interface:** For scripting and automation
- **Docker containers:** For microservice architectures

### **Q: Is the source code available?**
A: Yes, this is an open-source project. Check our GitHub repository for the complete source code, documentation, and contribution guidelines.

## 📊 Performance & Limitations

### **Q: What are the current limitations?**
A: **Known limitations:**
- English-only queries (primarily)
- Processing time scales with video length
- Accuracy depends on video quality
- Memory usage for very large videos
- Some complex scenes may be challenging

### **Q: How accurate is the search?**
A: **Typical accuracy:** 85-95% for clear, well-lit videos with distinct objects. Accuracy varies based on:
- Video quality and lighting
- Object clarity and size
- Query specificity
- Model configuration

### **Q: Can I process 4K videos?**
A: Yes, but consider:
- **Processing time:** Significantly longer than lower resolutions
- **Memory usage:** May require chunked processing
- **Storage:** Large output files
- **Recommendation:** Use lower resolution for processing, keep originals for final clips

## 🆘 Support & Community

### **Q: Where can I get help?**
A: **Support channels:**
1. **Documentation:** Comprehensive guides and tutorials
2. **GitHub Issues:** Bug reports and feature requests
3. **Community Discussions:** User community support
4. **Professional Support:** Enterprise support available

### **Q: How do I report bugs or request features?**
A: Create an issue on our GitHub repository with:
- Detailed description of the problem/request
- Steps to reproduce (for bugs)
- System information (`python test_installation.py`)
- Error logs if applicable

### **Q: Can I contribute to the project?**
A: Absolutely! We welcome:
- **Code contributions:** Bug fixes, new features
- **Documentation:** Improvements, translations
- **Testing:** Bug reports, feature testing
- **Community support:** Helping other users

### **Q: Is commercial use allowed?**
A: Yes, this project is released under the MIT License, which allows commercial use. Please review the license terms for full details.

## 🔮 Future Development

### **Q: What features are planned for future releases?**
A: **Roadmap includes:**
- Multi-language query support
- Advanced video analytics
- Cloud storage integration
- Mobile applications
- Enhanced AI models
- Performance optimizations

### **Q: How often is the project updated?**
A: We aim for regular updates with bug fixes, performance improvements, and new features. Check our [Changelog](changelog.md) for release history.

### **Q: Can I request specific features?**
A: Yes! Create a feature request on GitHub Issues. We prioritize features based on community interest and technical feasibility.

---

**Still have questions?** Check our [Complete User Guide](COMPLETE_USER_GUIDE.md) or ask in our community discussions!
