# AI-Powered Video Content Search - Docker Environment Configuration
# Copy this file to .env and customize the values for your deployment

# =============================================================================
# Application Configuration
# =============================================================================

# Build Configuration
INSTALL_GPU=false                    # Set to true for GPU support
BUILD_ENV=production                 # production, development, or testing

# Web Interface
WEB_PORT=8501                       # Port for Streamlit web interface
LOG_LEVEL=INFO                      # DEBUG, INFO, WARNING, ERROR

# Video Input Directory
VIDEO_INPUT_DIR=./videos            # Directory containing your video files

# =============================================================================
# Optional Services Configuration
# =============================================================================

# Redis Cache (enable with: docker-compose --profile with-redis up)
REDIS_PORT=6379

# PostgreSQL Database (enable with: docker-compose --profile with-database up)
POSTGRES_PORT=5432
POSTGRES_DB=ai_video_search
POSTGRES_USER=postgres
POSTGRES_PASSWORD=change_this_password

# Nginx Reverse Proxy (enable with: docker-compose --profile with-nginx up)
NGINX_PORT=80
NGINX_SSL_PORT=443

# Monitoring (enable with: docker-compose --profile with-monitoring up)
PROMETHEUS_PORT=9090
GRAFANA_PORT=3000
GRAFANA_USER=admin
GRAFANA_PASSWORD=change_this_password

# =============================================================================
# GPU Configuration (NVIDIA only)
# =============================================================================

# Uncomment and configure if using NVIDIA GPU
# NVIDIA_VISIBLE_DEVICES=all
# NVIDIA_DRIVER_CAPABILITIES=compute,utility

# =============================================================================
# Performance Tuning
# =============================================================================

# Memory limits (optional)
# MEMORY_LIMIT=8g
# MEMORY_RESERVATION=4g

# CPU limits (optional)
# CPU_LIMIT=4.0
# CPU_RESERVATION=2.0

# =============================================================================
# Security Configuration
# =============================================================================

# SSL Configuration (for production)
# SSL_CERT_PATH=./docker/nginx/ssl/cert.pem
# SSL_KEY_PATH=./docker/nginx/ssl/key.pem

# Authentication (if implementing)
# AUTH_ENABLED=false
# JWT_SECRET=your_jwt_secret_here
# SESSION_TIMEOUT=3600

# =============================================================================
# Storage Configuration
# =============================================================================

# Data persistence paths
DATA_DIR=./data
OUTPUT_DIR=./static/output_clips
TEMP_DIR=./temp_videos
LOGS_DIR=./logs

# Cache configuration
CACHE_SIZE_MB=2048
CACHE_TTL_SECONDS=3600

# =============================================================================
# Model Configuration
# =============================================================================

# CLIP Model
CLIP_MODEL=openai/clip-vit-base-patch32

# YOLO Model
YOLO_MODEL=yolov8n.pt

# Model cache directory
MODEL_CACHE_DIR=./models/cache

# =============================================================================
# Advanced Configuration
# =============================================================================

# Processing configuration
DEFAULT_FRAME_INTERVAL=30
DEFAULT_SIMILARITY_THRESHOLD=0.2
MAX_VIDEO_SIZE_MB=10000

# Parallel processing
ENABLE_PARALLEL_PROCESSING=true
MAX_WORKERS=4

# Memory management
ENABLE_MEMORY_MONITORING=true
AUTO_CLEANUP_THRESHOLD=0.8

# =============================================================================
# Development Configuration
# =============================================================================

# Development mode settings (only for development builds)
# DEBUG=true
# RELOAD_ON_CHANGE=true
# PROFILING_ENABLED=false

# Testing configuration
# RUN_TESTS_ON_START=false
# TEST_VIDEO_PATH=./test_data/sample_video.mp4

# =============================================================================
# Cloud Configuration (for cloud deployments)
# =============================================================================

# AWS Configuration
# AWS_ACCESS_KEY_ID=your_access_key
# AWS_SECRET_ACCESS_KEY=your_secret_key
# AWS_DEFAULT_REGION=us-east-1
# S3_BUCKET=your-video-bucket

# Azure Configuration
# AZURE_STORAGE_ACCOUNT=your_storage_account
# AZURE_STORAGE_KEY=your_storage_key
# AZURE_CONTAINER=your-container

# Google Cloud Configuration
# GOOGLE_APPLICATION_CREDENTIALS=./gcp-credentials.json
# GCS_BUCKET=your-video-bucket

# =============================================================================
# Monitoring and Alerting
# =============================================================================

# Prometheus metrics
ENABLE_METRICS=false
METRICS_PORT=9091

# Health check configuration
HEALTH_CHECK_INTERVAL=30
HEALTH_CHECK_TIMEOUT=10
HEALTH_CHECK_RETRIES=3

# Alerting (if using external services)
# SLACK_WEBHOOK_URL=https://hooks.slack.com/services/...
# EMAIL_ALERTS_ENABLED=false
# SMTP_SERVER=smtp.gmail.com
# SMTP_PORT=587
# SMTP_USERNAME=<EMAIL>
# SMTP_PASSWORD=your_app_password

# =============================================================================
# Backup Configuration
# =============================================================================

# Automatic backup settings
# BACKUP_ENABLED=false
# BACKUP_SCHEDULE=0 2 * * *  # Daily at 2 AM
# BACKUP_RETENTION_DAYS=30
# BACKUP_DESTINATION=./backups

# =============================================================================
# Rate Limiting and Security
# =============================================================================

# Rate limiting
# RATE_LIMIT_ENABLED=false
# RATE_LIMIT_REQUESTS_PER_MINUTE=60

# CORS settings
# CORS_ENABLED=false
# CORS_ORIGINS=http://localhost:3000,https://yourdomain.com

# Security headers
# SECURITY_HEADERS_ENABLED=true
# HSTS_ENABLED=true
# CSP_ENABLED=true
