#!/usr/bin/env python3
"""
Requirements Management System for AI-Powered Video Content Search.
Generates optimized requirements based on system capabilities and user preferences.
"""

import os
import sys
import platform
import subprocess
import json
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
import pkg_resources


class RequirementsManager:
    """Manages requirements generation and optimization."""
    
    def __init__(self):
        self.platform = platform.system().lower()
        self.architecture = platform.machine()
        self.python_version = sys.version_info
        self.gpu_info = self._detect_gpu_capabilities()
        
    def _detect_gpu_capabilities(self) -> Dict[str, Any]:
        """Detect GPU capabilities and drivers."""
        gpu_info = {
            'nvidia_cuda': False,
            'amd_rocm': False,
            'intel_gpu': False,
            'apple_mps': False,
            'cuda_version': None,
            'gpu_memory': None,
            'gpu_name': None
        }
        
        # Check NVIDIA CUDA
        try:
            result = subprocess.run(['nvidia-smi', '--query-gpu=name,memory.total', '--format=csv,noheader,nounits'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                if lines and lines[0]:
                    parts = lines[0].split(', ')
                    gpu_info['nvidia_cuda'] = True
                    gpu_info['gpu_name'] = parts[0]
                    gpu_info['gpu_memory'] = int(parts[1]) if len(parts) > 1 else None
                    
                    # Get CUDA version
                    try:
                        cuda_result = subprocess.run(['nvcc', '--version'], 
                                                   capture_output=True, text=True, timeout=5)
                        if cuda_result.returncode == 0:
                            # Parse CUDA version from output
                            for line in cuda_result.stdout.split('\n'):
                                if 'release' in line.lower():
                                    import re
                                    match = re.search(r'release (\d+\.\d+)', line)
                                    if match:
                                        gpu_info['cuda_version'] = match.group(1)
                    except:
                        pass
        except:
            pass
        
        # Check AMD ROCm
        try:
            result = subprocess.run(['rocm-smi', '--showproductname'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                gpu_info['amd_rocm'] = True
        except:
            pass
        
        # Check Apple Silicon MPS
        if self.platform == 'darwin' and self.architecture == 'arm64':
            gpu_info['apple_mps'] = True
        
        return gpu_info
    
    def generate_base_requirements(self) -> List[str]:
        """Generate base requirements common to all installations."""
        return [
            # Core ML/AI packages
            "torch>=2.0.0",
            "torchvision>=0.15.0",
            "transformers>=4.30.0",
            
            # Computer Vision
            "opencv-python>=4.8.0",
            "Pillow>=10.0.0",
            
            # Video Processing
            "moviepy>=1.0.3",
            "ffmpeg-python>=0.2.0",
            "ultralytics>=8.0.0",
            
            # Web Interface
            "streamlit>=1.28.0",
            
            # Scientific Computing
            "numpy>=1.24.0",
            "scipy>=1.10.0",
            "matplotlib>=3.7.0",
            "pandas>=2.0.0",
            "scikit-learn>=1.3.0",
            
            # Utilities
            "requests>=2.31.0",
            "tqdm>=4.65.0",
            "psutil>=5.9.0",
            "plotly>=5.15.0",
            
            # YouTube support
            "yt-dlp>=2023.7.6",
        ]
    
    def generate_gpu_requirements(self) -> List[str]:
        """Generate GPU-specific requirements."""
        requirements = []
        
        if self.gpu_info['nvidia_cuda']:
            # CUDA-specific packages
            cuda_version = self.gpu_info.get('cuda_version', '11.8')
            if cuda_version.startswith('12.'):
                requirements.extend([
                    "torch>=2.0.0+cu121",
                    "torchvision>=0.15.0+cu121",
                ])
            elif cuda_version.startswith('11.'):
                requirements.extend([
                    "torch>=2.0.0+cu118",
                    "torchvision>=0.15.0+cu118",
                ])
            
            # Additional CUDA tools
            requirements.extend([
                "nvidia-ml-py>=12.535.0",
                "cupy-cuda11x>=12.0.0",  # Adjust based on CUDA version
            ])
        
        elif self.gpu_info['amd_rocm']:
            # ROCm-specific packages
            requirements.extend([
                "torch>=2.0.0+rocm5.4.2",
                "torchvision>=0.15.0+rocm5.4.2",
            ])
        
        elif self.gpu_info['apple_mps']:
            # Apple Silicon optimizations
            requirements.extend([
                "torch>=2.0.0",  # MPS support built-in
                "torchvision>=0.15.0",
            ])
        
        return requirements
    
    def generate_platform_requirements(self) -> List[str]:
        """Generate platform-specific requirements."""
        requirements = []
        
        if self.platform == 'windows':
            requirements.extend([
                "pywin32>=306",
                "wmi>=1.5.1",
            ])
        elif self.platform == 'darwin':
            requirements.extend([
                "pyobjc-core>=9.2",  # For macOS integration
            ])
        elif self.platform == 'linux':
            requirements.extend([
                "python-magic>=0.4.27",  # File type detection
            ])
        
        return requirements
    
    def generate_development_requirements(self) -> List[str]:
        """Generate development and testing requirements."""
        return [
            # Testing
            "pytest>=7.4.0",
            "pytest-cov>=4.1.0",
            "pytest-mock>=3.11.0",
            "pytest-asyncio>=0.21.0",
            
            # Code Quality
            "black>=23.7.0",
            "flake8>=6.0.0",
            "mypy>=1.5.0",
            "isort>=5.12.0",
            
            # Documentation
            "sphinx>=7.1.0",
            "sphinx-rtd-theme>=1.3.0",
            
            # Development Tools
            "jupyter>=1.0.0",
            "ipython>=8.14.0",
            "pre-commit>=3.3.0",
            
            # Profiling and Debugging
            "memory-profiler>=0.61.0",
            "line-profiler>=4.1.0",
            "py-spy>=0.3.14",
        ]
    
    def generate_optional_requirements(self) -> Dict[str, List[str]]:
        """Generate optional requirements grouped by functionality."""
        return {
            'cloud': [
                "boto3>=1.28.0",  # AWS
                "azure-storage-blob>=12.17.0",  # Azure
                "google-cloud-storage>=2.10.0",  # GCP
            ],
            'database': [
                "sqlalchemy>=2.0.0",
                "sqlite3",  # Built-in, but listed for completeness
                "redis>=4.6.0",
            ],
            'api': [
                "fastapi>=0.101.0",
                "uvicorn>=0.23.0",
                "pydantic>=2.1.0",
            ],
            'monitoring': [
                "prometheus-client>=0.17.0",
                "grafana-api>=1.0.3",
                "sentry-sdk>=1.29.0",
            ],
            'advanced_ml': [
                "tensorflow>=2.13.0",
                "keras>=2.13.0",
                "onnx>=1.14.0",
                "onnxruntime>=1.15.0",
            ]
        }
    
    def create_requirements_file(self, 
                                include_gpu: bool = True,
                                include_dev: bool = False,
                                include_optional: Optional[List[str]] = None,
                                output_file: str = "requirements.txt") -> bool:
        """Create optimized requirements file."""
        
        print(f"🔧 Generating optimized requirements for {self.platform} {self.architecture}")
        
        # Start with base requirements
        all_requirements = self.generate_base_requirements()
        
        # Add GPU requirements if requested and available
        if include_gpu and (self.gpu_info['nvidia_cuda'] or self.gpu_info['amd_rocm'] or self.gpu_info['apple_mps']):
            gpu_reqs = self.generate_gpu_requirements()
            all_requirements.extend(gpu_reqs)
            print(f"   ✅ Added GPU requirements for {self._get_gpu_type()}")
        
        # Add platform-specific requirements
        platform_reqs = self.generate_platform_requirements()
        all_requirements.extend(platform_reqs)
        print(f"   ✅ Added {self.platform} platform requirements")
        
        # Add development requirements if requested
        if include_dev:
            dev_reqs = self.generate_development_requirements()
            all_requirements.extend(dev_reqs)
            print(f"   ✅ Added development requirements")
        
        # Add optional requirements if specified
        if include_optional:
            optional_reqs = self.generate_optional_requirements()
            for category in include_optional:
                if category in optional_reqs:
                    all_requirements.extend(optional_reqs[category])
                    print(f"   ✅ Added {category} requirements")
        
        # Remove duplicates while preserving order
        seen = set()
        unique_requirements = []
        for req in all_requirements:
            if req not in seen:
                seen.add(req)
                unique_requirements.append(req)
        
        # Write requirements file
        try:
            with open(output_file, 'w') as f:
                f.write(f"# AI-Powered Video Content Search - Requirements\n")
                f.write(f"# Generated for {self.platform} {self.architecture}\n")
                f.write(f"# Python {self.python_version.major}.{self.python_version.minor}.{self.python_version.micro}\n")
                
                if self.gpu_info['nvidia_cuda'] or self.gpu_info['amd_rocm'] or self.gpu_info['apple_mps']:
                    f.write(f"# GPU: {self._get_gpu_type()}\n")
                
                f.write(f"# Generated on: {self._get_timestamp()}\n\n")
                
                # Write base requirements
                f.write("# Core Dependencies\n")
                base_reqs = self.generate_base_requirements()
                for req in base_reqs:
                    if req in unique_requirements:
                        f.write(f"{req}\n")
                
                # Write GPU requirements
                if include_gpu:
                    gpu_reqs = self.generate_gpu_requirements()
                    if gpu_reqs:
                        f.write(f"\n# GPU Acceleration ({self._get_gpu_type()})\n")
                        for req in gpu_reqs:
                            if req in unique_requirements:
                                f.write(f"{req}\n")
                
                # Write platform requirements
                platform_reqs = self.generate_platform_requirements()
                if platform_reqs:
                    f.write(f"\n# Platform-specific ({self.platform})\n")
                    for req in platform_reqs:
                        if req in unique_requirements:
                            f.write(f"{req}\n")
                
                # Write development requirements
                if include_dev:
                    f.write(f"\n# Development Dependencies\n")
                    dev_reqs = self.generate_development_requirements()
                    for req in dev_reqs:
                        if req in unique_requirements:
                            f.write(f"{req}\n")
                
                # Write optional requirements
                if include_optional:
                    optional_reqs = self.generate_optional_requirements()
                    for category in include_optional:
                        if category in optional_reqs:
                            f.write(f"\n# Optional: {category.title()}\n")
                            for req in optional_reqs[category]:
                                if req in unique_requirements:
                                    f.write(f"{req}\n")
            
            print(f"   ✅ Requirements file created: {output_file}")
            print(f"   📊 Total packages: {len(unique_requirements)}")
            return True
            
        except Exception as e:
            print(f"   ❌ Failed to create requirements file: {e}")
            return False
    
    def _get_gpu_type(self) -> str:
        """Get human-readable GPU type."""
        if self.gpu_info['nvidia_cuda']:
            return f"NVIDIA CUDA {self.gpu_info.get('cuda_version', 'Unknown')}"
        elif self.gpu_info['amd_rocm']:
            return "AMD ROCm"
        elif self.gpu_info['apple_mps']:
            return "Apple Silicon MPS"
        else:
            return "CPU Only"
    
    def _get_timestamp(self) -> str:
        """Get current timestamp."""
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    def create_multiple_requirements_files(self):
        """Create multiple requirements files for different use cases."""
        configurations = [
            {
                'name': 'requirements.txt',
                'description': 'Standard installation',
                'include_gpu': True,
                'include_dev': False,
                'include_optional': None
            },
            {
                'name': 'requirements-dev.txt',
                'description': 'Development installation',
                'include_gpu': True,
                'include_dev': True,
                'include_optional': ['api', 'monitoring']
            },
            {
                'name': 'requirements-minimal.txt',
                'description': 'Minimal installation (CPU only)',
                'include_gpu': False,
                'include_dev': False,
                'include_optional': None
            },
            {
                'name': 'requirements-cloud.txt',
                'description': 'Cloud deployment',
                'include_gpu': True,
                'include_dev': False,
                'include_optional': ['cloud', 'api', 'monitoring']
            }
        ]
        
        print("🔧 Creating multiple requirements configurations...")
        
        for config in configurations:
            print(f"\n📝 {config['description']}:")
            success = self.create_requirements_file(
                include_gpu=config['include_gpu'],
                include_dev=config['include_dev'],
                include_optional=config['include_optional'],
                output_file=config['name']
            )
            
            if success:
                print(f"   ✅ Created {config['name']}")
            else:
                print(f"   ❌ Failed to create {config['name']}")
    
    def analyze_current_environment(self) -> Dict[str, Any]:
        """Analyze current Python environment and installed packages."""
        analysis = {
            'python_version': f"{self.python_version.major}.{self.python_version.minor}.{self.python_version.micro}",
            'platform': f"{self.platform} {self.architecture}",
            'gpu_info': self.gpu_info,
            'installed_packages': {},
            'missing_packages': [],
            'outdated_packages': []
        }
        
        # Get installed packages
        try:
            installed_packages = {pkg.project_name: pkg.version for pkg in pkg_resources.working_set}
            analysis['installed_packages'] = installed_packages
        except Exception as e:
            print(f"Warning: Could not analyze installed packages: {e}")
        
        return analysis
    
    def print_system_summary(self):
        """Print comprehensive system summary."""
        print(f"\n🖥️  System Summary:")
        print(f"   Platform: {self.platform} {self.architecture}")
        print(f"   Python: {self.python_version.major}.{self.python_version.minor}.{self.python_version.micro}")
        print(f"   GPU: {self._get_gpu_type()}")
        
        if self.gpu_info['nvidia_cuda']:
            print(f"   GPU Name: {self.gpu_info.get('gpu_name', 'Unknown')}")
            if self.gpu_info.get('gpu_memory'):
                print(f"   GPU Memory: {self.gpu_info['gpu_memory']} MB")


def main():
    """Main function for requirements management."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Requirements Management for AI Video Search")
    parser.add_argument('--create-all', action='store_true', help='Create all requirements configurations')
    parser.add_argument('--gpu', action='store_true', default=True, help='Include GPU requirements')
    parser.add_argument('--dev', action='store_true', help='Include development requirements')
    parser.add_argument('--optional', nargs='+', choices=['cloud', 'database', 'api', 'monitoring', 'advanced_ml'],
                       help='Include optional requirements')
    parser.add_argument('--output', default='requirements.txt', help='Output file name')
    parser.add_argument('--analyze', action='store_true', help='Analyze current environment')
    
    args = parser.parse_args()
    
    manager = RequirementsManager()
    manager.print_system_summary()
    
    if args.analyze:
        analysis = manager.analyze_current_environment()
        print(f"\n📊 Environment Analysis:")
        print(f"   Installed packages: {len(analysis['installed_packages'])}")
    
    if args.create_all:
        manager.create_multiple_requirements_files()
    else:
        manager.create_requirements_file(
            include_gpu=args.gpu,
            include_dev=args.dev,
            include_optional=args.optional,
            output_file=args.output
        )


if __name__ == "__main__":
    main()
