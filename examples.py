"""
Example usage scripts for the AI-powered video content search application.
This file demonstrates various ways to use the search engine programmatically.
"""

import os
import sys

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.clip_match import VideoSearchEngine
from utils.frame_extraction import FrameExtractor
from models.clip_model import CLIPMatch<PERSON>


def example_basic_search():
    """Example: Basic video search with default settings."""
    print("🔍 Example: Basic Video Search")
    print("-" * 40)
    
    # Initialize search engine
    search_engine = VideoSearchEngine()
    
    # Example video path (replace with your video)
    video_path = "path/to/your/video.mp4"
    
    if not os.path.exists(video_path):
        print(f"⚠️  Video not found: {video_path}")
        print("   Please update the video_path variable with a real video file")
        return
    
    # Search for content
    results = search_engine.search_video(
        video_path=video_path,
        query="person walking",
        similarity_threshold=0.2,
        top_k=10
    )
    
    # Display results
    print(f"Found {len(results['matches'])} matches")
    for i, match in enumerate(results['matches'][:5]):
        print(f"  {i+1}. Time: {match['time_formatted']} | Score: {match['similarity_score']:.3f}")


def example_batch_search():
    """Example: Search for multiple queries in the same video."""
    print("\n🔍 Example: Batch Search")
    print("-" * 40)
    
    search_engine = VideoSearchEngine()
    video_path = "path/to/your/video.mp4"
    
    if not os.path.exists(video_path):
        print(f"⚠️  Video not found: {video_path}")
        return
    
    # Multiple queries
    queries = [
        "person walking",
        "red car",
        "blue sky",
        "green trees"
    ]
    
    # Batch search
    batch_results = search_engine.batch_search(
        video_path=video_path,
        queries=queries,
        similarity_threshold=0.2
    )
    
    # Display results
    for query, results in batch_results.items():
        print(f"'{query}': {len(results['matches'])} matches")


def example_custom_settings():
    """Example: Search with custom settings and clip generation."""
    print("\n🔍 Example: Custom Settings with Clips")
    print("-" * 40)
    
    # Custom search engine with different settings
    search_engine = VideoSearchEngine(
        frame_interval=15,  # More frames for higher precision
        output_dir="custom_output"
    )
    
    video_path = "path/to/your/video.mp4"
    
    if not os.path.exists(video_path):
        print(f"⚠️  Video not found: {video_path}")
        return
    
    # Search with clip generation
    results = search_engine.search_video(
        video_path=video_path,
        query="dog running",
        similarity_threshold=0.15,  # Lower threshold for more results
        top_k=20,
        create_clips=True,
        clip_duration=5.0,  # 5-second clips
        create_thumbnails=True
    )
    
    print(f"Found {len(results['matches'])} matches")
    print(f"Created {len(results['clips'])} video clips")
    print(f"Created {len(results['thumbnails'])} thumbnails")


def example_frame_extraction_only():
    """Example: Extract frames without searching."""
    print("\n🎞️ Example: Frame Extraction Only")
    print("-" * 40)
    
    video_path = "path/to/your/video.mp4"
    
    if not os.path.exists(video_path):
        print(f"⚠️  Video not found: {video_path}")
        return
    
    # Extract frames
    extractor = FrameExtractor(every_n_frames=60)  # Every 2 seconds at 30fps
    
    # Get video info
    info = extractor.get_video_info(video_path)
    print(f"Video duration: {info['duration_seconds']:.1f} seconds")
    print(f"Resolution: {info['width']}x{info['height']}")
    print(f"FPS: {info['fps']:.1f}")
    
    # Extract frames
    frames = extractor.extract_frames(video_path)
    print(f"Extracted {len(frames)} frames")
    
    # Save some frames as images
    import cv2
    for i, (frame_idx, frame_array, timestamp) in enumerate(frames[:5]):
        filename = f"frame_{i+1}_time_{timestamp:.1f}s.jpg"
        # Convert RGB to BGR for OpenCV
        frame_bgr = cv2.cvtColor(frame_array, cv2.COLOR_RGB2BGR)
        cv2.imwrite(filename, frame_bgr)
        print(f"Saved: {filename}")


def example_clip_model_only():
    """Example: Use CLIP model directly for image-text matching."""
    print("\n🤖 Example: CLIP Model Direct Usage")
    print("-" * 40)
    
    # Initialize CLIP matcher
    matcher = CLIPMatcher()
    
    # Create some test images (you can replace with real images)
    import numpy as np
    
    # Red image
    red_image = np.full((224, 224, 3), [255, 0, 0], dtype=np.uint8)
    
    # Blue image  
    blue_image = np.full((224, 224, 3), [0, 0, 255], dtype=np.uint8)
    
    # Green image
    green_image = np.full((224, 224, 3), [0, 255, 0], dtype=np.uint8)
    
    images = [red_image, blue_image, green_image]
    
    # Test different queries
    queries = ["red color", "blue color", "green color"]
    
    for query in queries:
        print(f"\nQuery: '{query}'")
        
        # Encode text and images
        text_features = matcher.encode_text(query)
        image_features = matcher.encode_images(images)
        
        # Compute similarities
        similarities = matcher.compute_similarity(text_features, image_features)
        similarities = similarities.cpu().numpy()
        
        # Show results
        for i, sim in enumerate(similarities):
            color = ["red", "blue", "green"][i]
            print(f"  {color} image: {sim:.3f}")


def example_video_analysis_pipeline():
    """Example: Complete video analysis pipeline."""
    print("\n🔄 Example: Complete Analysis Pipeline")
    print("-" * 40)
    
    video_path = "path/to/your/video.mp4"
    
    if not os.path.exists(video_path):
        print(f"⚠️  Video not found: {video_path}")
        return
    
    # Step 1: Analyze video content
    search_engine = VideoSearchEngine()
    
    # Step 2: Search for different types of content
    content_queries = [
        "people",
        "vehicles", 
        "buildings",
        "nature",
        "indoor scenes",
        "outdoor scenes"
    ]
    
    print("Analyzing video content...")
    analysis_results = {}
    
    for query in content_queries:
        results = search_engine.search_video(
            video_path=video_path,
            query=query,
            similarity_threshold=0.25,
            top_k=5,
            create_clips=False,
            create_thumbnails=False
        )
        
        analysis_results[query] = len(results['matches'])
        print(f"  {query}: {len(results['matches'])} matches")
    
    # Step 3: Generate summary
    print("\n📊 Video Content Summary:")
    total_matches = sum(analysis_results.values())
    
    if total_matches > 0:
        for content_type, count in analysis_results.items():
            percentage = (count / total_matches) * 100
            print(f"  {content_type}: {percentage:.1f}% ({count} matches)")
    else:
        print("  No content detected with current thresholds")


def main():
    """Run all examples."""
    print("🎬 AI Video Search - Example Usage")
    print("=" * 50)
    
    print("\n💡 Note: Update video paths in the examples to use real video files")
    print("   You can use the test_application.py to create test videos")
    
    # Run examples (comment out any you don't want to run)
    example_basic_search()
    example_batch_search() 
    example_custom_settings()
    example_frame_extraction_only()
    example_clip_model_only()
    example_video_analysis_pipeline()
    
    print("\n✅ All examples completed!")
    print("\n💡 Next steps:")
    print("   1. Replace 'path/to/your/video.mp4' with real video files")
    print("   2. Modify the examples for your specific use case")
    print("   3. Run 'python main.py --web' for the interactive interface")


if __name__ == "__main__":
    main()
