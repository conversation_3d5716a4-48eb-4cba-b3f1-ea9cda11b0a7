#!/usr/bin/env python3
"""
Environment setup and dependency management for AI-Powered Video Content Search.
Handles different installation scenarios and system configurations.
"""

import os
import sys
import subprocess
import platform
import shutil
import json
import urllib.request
from pathlib import Path
from typing import Dict, List, Optional, Tuple


class EnvironmentSetup:
    """Manages environment setup and dependency installation."""
    
    def __init__(self):
        """Initialize environment setup."""
        self.platform = platform.system().lower()
        self.python_version = sys.version_info
        self.architecture = platform.machine()
        self.is_conda = 'conda' in sys.executable or 'CONDA_DEFAULT_ENV' in os.environ
        self.is_venv = hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix)
        
        # Package managers
        self.package_managers = {
            'pip': self._check_pip(),
            'conda': self._check_conda(),
            'poetry': self._check_poetry()
        }
        
        print("🔧 Environment Setup for AI-Powered Video Content Search")
        print(f"Platform: {self.platform} {self.architecture}")
        print(f"Python: {self.python_version.major}.{self.python_version.minor}.{self.python_version.micro}")
        print(f"Virtual Environment: {'Conda' if self.is_conda else 'venv' if self.is_venv else 'None'}")
    
    def _check_pip(self) -> bool:
        """Check if pip is available."""
        try:
            subprocess.run([sys.executable, '-m', 'pip', '--version'], 
                         capture_output=True, check=True)
            return True
        except (subprocess.CalledProcessError, FileNotFoundError):
            return False
    
    def _check_conda(self) -> bool:
        """Check if conda is available."""
        return shutil.which('conda') is not None
    
    def _check_poetry(self) -> bool:
        """Check if poetry is available."""
        return shutil.which('poetry') is not None
    
    def detect_gpu_support(self) -> Dict[str, bool]:
        """Detect available GPU support."""
        gpu_support = {
            'cuda': False,
            'rocm': False,
            'mps': False,
            'intel_gpu': False
        }
        
        try:
            # Check NVIDIA CUDA
            if shutil.which('nvidia-smi'):
                result = subprocess.run(['nvidia-smi'], capture_output=True, text=True)
                if result.returncode == 0:
                    gpu_support['cuda'] = True
                    print("✅ NVIDIA GPU detected")
        except:
            pass
        
        try:
            # Check AMD ROCm
            if shutil.which('rocm-smi'):
                gpu_support['rocm'] = True
                print("✅ AMD GPU (ROCm) detected")
        except:
            pass
        
        # Check Apple Silicon MPS
        if self.platform == 'darwin' and self.architecture == 'arm64':
            gpu_support['mps'] = True
            print("✅ Apple Silicon (MPS) detected")
        
        return gpu_support
    
    def create_optimized_requirements(self, gpu_support: Dict[str, bool]) -> str:
        """Create optimized requirements based on system capabilities."""
        base_requirements = [
            "torch>=1.12.0",
            "torchvision>=0.13.0",
            "transformers>=4.20.0",
            "opencv-python>=4.6.0",
            "moviepy>=1.0.3",
            "streamlit>=1.25.0",
            "Pillow>=9.0.0",
            "ffmpeg-python>=0.2.0",
            "ultralytics>=8.0.0",
            "numpy>=1.21.0",
            "scipy>=1.8.0",
            "matplotlib>=3.5.0",
            "requests>=2.28.0",
            "tqdm>=4.64.0",
            "yt-dlp>=2023.1.6",
            "scikit-learn>=1.1.0",
            "pandas>=1.4.0",
            "plotly>=5.0.0",
            "psutil>=5.8.0"
        ]
        
        # Add GPU-specific packages
        if gpu_support['cuda']:
            # CUDA support
            base_requirements.extend([
                "torch[cuda]",
                "torchvision[cuda]"
            ])
        elif gpu_support['rocm']:
            # ROCm support
            base_requirements.extend([
                "torch[rocm]"
            ])
        
        # Platform-specific packages
        if self.platform == 'windows':
            base_requirements.extend([
                "pywin32>=227"
            ])
        
        # Development packages (optional)
        dev_requirements = [
            "pytest>=6.0",
            "pytest-cov>=2.0",
            "black>=21.0",
            "flake8>=3.8",
            "mypy>=0.800",
            "jupyter>=1.0.0"
        ]
        
        # Create requirements content
        content = "# AI-Powered Video Content Search - Requirements\n"
        content += "# Generated automatically based on system capabilities\n\n"
        content += "# Core dependencies\n"
        for req in base_requirements:
            content += f"{req}\n"
        
        content += "\n# Development dependencies (optional)\n"
        content += "# Uncomment to install development tools\n"
        for req in dev_requirements:
            content += f"# {req}\n"
        
        return content
    
    def install_system_dependencies(self) -> bool:
        """Install system-level dependencies."""
        print("\n🔧 Checking system dependencies...")
        
        required_tools = ['git', 'ffmpeg']
        missing_tools = []
        
        for tool in required_tools:
            if not shutil.which(tool):
                missing_tools.append(tool)
            else:
                print(f"✅ {tool} found")
        
        if missing_tools:
            print(f"⚠️  Missing tools: {', '.join(missing_tools)}")
            
            if self.platform == 'windows':
                print("\nWindows installation options:")
                print("1. Chocolatey: choco install git ffmpeg")
                print("2. Scoop: scoop install git ffmpeg")
                print("3. Manual installation from official websites")
            elif self.platform == 'darwin':
                print("\nmacOS installation options:")
                print("1. Homebrew: brew install git ffmpeg")
                print("2. MacPorts: sudo port install git ffmpeg")
            elif self.platform == 'linux':
                print("\nLinux installation options:")
                print("Ubuntu/Debian: sudo apt install git ffmpeg")
                print("CentOS/RHEL: sudo yum install git ffmpeg")
                print("Arch: sudo pacman -S git ffmpeg")
            
            return False
        
        return True
    
    def setup_python_environment(self) -> bool:
        """Set up Python environment and install packages."""
        print("\n📦 Setting up Python environment...")
        
        # Detect GPU support
        gpu_support = self.detect_gpu_support()
        
        # Create optimized requirements
        requirements_content = self.create_optimized_requirements(gpu_support)
        
        # Save requirements file
        with open('requirements_optimized.txt', 'w') as f:
            f.write(requirements_content)
        
        print("✅ Created optimized requirements file")
        
        # Choose installation method
        if self.package_managers['conda'] and self.is_conda:
            return self._install_with_conda()
        elif self.package_managers['poetry']:
            return self._install_with_poetry()
        elif self.package_managers['pip']:
            return self._install_with_pip()
        else:
            print("❌ No suitable package manager found")
            return False
    
    def _install_with_pip(self) -> bool:
        """Install packages using pip."""
        print("📦 Installing with pip...")
        
        try:
            # Upgrade pip
            subprocess.run([
                sys.executable, '-m', 'pip', 'install', '--upgrade', 'pip'
            ], check=True)
            
            # Install requirements
            subprocess.run([
                sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'
            ], check=True)
            
            print("✅ Packages installed successfully")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"❌ Installation failed: {e}")
            return False
    
    def _install_with_conda(self) -> bool:
        """Install packages using conda."""
        print("📦 Installing with conda...")
        
        try:
            # Install conda packages first
            conda_packages = [
                'pytorch', 'torchvision', 'numpy', 'scipy', 'matplotlib',
                'opencv', 'pillow', 'pandas', 'scikit-learn'
            ]
            
            subprocess.run([
                'conda', 'install', '-y', '-c', 'pytorch', '-c', 'conda-forge'
            ] + conda_packages, check=True)
            
            # Install remaining packages with pip
            pip_packages = [
                'transformers', 'streamlit', 'ultralytics', 'yt-dlp',
                'ffmpeg-python', 'moviepy', 'plotly', 'psutil'
            ]
            
            subprocess.run([
                sys.executable, '-m', 'pip', 'install'
            ] + pip_packages, check=True)
            
            print("✅ Packages installed successfully")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"❌ Installation failed: {e}")
            return False
    
    def _install_with_poetry(self) -> bool:
        """Install packages using poetry."""
        print("📦 Installing with poetry...")
        
        try:
            # Initialize poetry project if needed
            if not Path('pyproject.toml').exists():
                subprocess.run(['poetry', 'init', '--no-interaction'], check=True)
            
            # Install dependencies
            subprocess.run(['poetry', 'install'], check=True)
            
            print("✅ Packages installed successfully")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"❌ Installation failed: {e}")
            return False
    
    def create_activation_scripts(self) -> None:
        """Create environment activation scripts."""
        print("\n🚀 Creating activation scripts...")
        
        if self.platform == 'windows':
            # Windows batch file
            with open('activate_env.bat', 'w') as f:
                f.write('@echo off\n')
                if self.is_conda:
                    f.write('call conda activate\n')
                elif self.is_venv:
                    f.write('call venv\\Scripts\\activate\n')
                f.write('echo Environment activated!\n')
                f.write('echo Run "python main.py --web" to start the application\n')
        else:
            # Unix shell script
            with open('activate_env.sh', 'w') as f:
                f.write('#!/bin/bash\n')
                if self.is_conda:
                    f.write('source activate\n')
                elif self.is_venv:
                    f.write('source venv/bin/activate\n')
                f.write('echo "Environment activated!"\n')
                f.write('echo "Run \'python main.py --web\' to start the application"\n')
            
            os.chmod('activate_env.sh', 0o755)
        
        print("✅ Activation scripts created")
    
    def verify_installation(self) -> bool:
        """Verify the installation by testing imports."""
        print("\n🧪 Verifying installation...")
        
        test_imports = [
            'torch', 'torchvision', 'transformers', 'cv2', 'streamlit',
            'PIL', 'moviepy.editor', 'ultralytics', 'numpy', 'scipy'
        ]
        
        failed_imports = []
        
        for module in test_imports:
            try:
                __import__(module)
                print(f"✅ {module}")
            except ImportError:
                failed_imports.append(module)
                print(f"❌ {module}")
        
        if failed_imports:
            print(f"\n⚠️  Failed imports: {', '.join(failed_imports)}")
            return False
        
        print("\n✅ All imports successful!")
        return True
    
    def run_setup(self) -> bool:
        """Run the complete setup process."""
        print("\n🚀 Starting environment setup...")
        
        # Check system dependencies
        if not self.install_system_dependencies():
            print("\n⚠️  Please install missing system dependencies and try again")
            return False
        
        # Setup Python environment
        if not self.setup_python_environment():
            print("\n❌ Python environment setup failed")
            return False
        
        # Create activation scripts
        self.create_activation_scripts()
        
        # Verify installation
        if not self.verify_installation():
            print("\n⚠️  Installation verification failed")
            return False
        
        print("\n🎉 Environment setup completed successfully!")
        return True


def main():
    """Main setup function."""
    setup = EnvironmentSetup()
    
    try:
        success = setup.run_setup()
        if success:
            print("\n✅ Setup completed! You can now run the application.")
        else:
            print("\n❌ Setup failed. Please check the errors above.")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n⚠️  Setup interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
