# 🎬 AI-Powered Video Content Search - Complete User Guide

Welcome to the comprehensive user guide for AI-Powered Video Content Search Pro! This guide will walk you through every feature and capability of the application.

## 📋 Table of Contents

1. [Getting Started](#getting-started)
2. [Web Interface Guide](#web-interface-guide)
3. [Command Line Interface](#command-line-interface)
4. [Live Video Detection](#live-video-detection)
5. [Batch Processing](#batch-processing)
6. [Advanced Features](#advanced-features)
7. [Configuration & Settings](#configuration--settings)
8. [Performance Optimization](#performance-optimization)
9. [Troubleshooting](#troubleshooting)
10. [Tips & Best Practices](#tips--best-practices)

## 🚀 Getting Started

### System Requirements

**Minimum Requirements:**
- Python 3.8+
- 4GB RAM
- 2GB free disk space
- Internet connection (for model downloads)

**Recommended Requirements:**
- Python 3.10+
- 8GB+ RAM
- GPU with 4GB+ VRAM (NVIDIA, AMD, or Apple Silicon)
- 10GB+ free disk space
- High-speed internet

### Installation Options

#### Option 1: Automated Installation (Recommended)

**Windows:**
```batch
git clone https://github.com/yourusername/ai-video-search.git
cd ai-video-search
install.bat
```

**Linux/macOS:**
```bash
git clone https://github.com/yourusername/ai-video-search.git
cd ai-video-search
chmod +x install.sh
./install.sh
```

#### Option 2: Manual Installation

1. **Clone Repository:**
   ```bash
   git clone https://github.com/yourusername/ai-video-search.git
   cd ai-video-search
   ```

2. **Create Virtual Environment:**
   ```bash
   python -m venv venv
   source venv/bin/activate  # Linux/macOS
   venv\Scripts\activate     # Windows
   ```

3. **Install Dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

4. **Test Installation:**
   ```bash
   python test_application.py
   ```

## 🌐 Web Interface Guide

### Starting the Web Interface

```bash
python main.py --web
# or use launcher scripts
./launch_web.sh      # Linux/macOS
launch_web.bat       # Windows
```

Open your browser to `http://localhost:8501`

### Interface Overview

The web interface consists of five main tabs:

#### 1. 📁 Video File Search

**Features:**
- Drag-and-drop video upload
- Support for multiple video formats (MP4, AVI, MOV, MKV, WMV, FLV, WebM)
- Real-time progress tracking
- Advanced search settings
- Results visualization

**How to Use:**
1. **Upload Video:** Drag and drop or click to select a video file
2. **Enter Query:** Type your search query (e.g., "red car", "person walking")
3. **Adjust Settings:**
   - **Similarity Threshold:** 0.1 (more results) to 0.5 (fewer, more accurate results)
   - **Max Results:** Number of matches to return
   - **Frame Interval:** How often to sample frames (lower = more thorough)
   - **Create Clips:** Generate video segments of matches
   - **Create Thumbnails:** Extract still images of matches
4. **Search:** Click the search button and wait for results
5. **View Results:** Browse matches with timestamps and similarity scores

#### 2. 📹 Live Video Detection

**Features:**
- Real-time camera detection
- RTSP stream support
- Pause/resume functionality
- Configurable detection parameters
- Live results display

**How to Use:**
1. **Select Source:**
   - **Webcam:** Choose camera index (usually 0 for built-in camera)
   - **RTSP Stream:** Enter stream URL (e.g., `rtsp://*************:554/stream`)
2. **Enter Query:** What to detect in real-time
3. **Configure Settings:**
   - **Detection Interval:** How often to analyze frames
   - **Max Results:** Maximum detections to keep
   - **Confidence Threshold:** Minimum detection confidence
4. **Start Detection:** Begin real-time analysis
5. **Control Playback:** Use pause/resume controls
6. **View Results:** See live detections with timestamps

#### 3. 📊 Batch Processing

**Two Processing Modes:**

**Multiple Videos, One Query:**
- Upload multiple video files
- Apply the same search query to all videos
- Compare results across videos

**One Video, Multiple Queries:**
- Upload one video file
- Search for multiple different objects/scenes
- Comprehensive analysis of video content

#### 4. 📈 Analytics & History

**Features:**
- Search history tracking
- Performance metrics
- Processing time trends
- Results visualization
- Export capabilities

**Available Analytics:**
- Total searches performed
- Average processing time
- Memory usage patterns
- Cache hit rates
- Error tracking

#### 5. ⚙️ Advanced Settings

**Model Settings:**
- CLIP model selection
- YOLO model configuration
- Device selection (CPU/GPU)
- Model optimization options

**Performance Settings:**
- GPU acceleration
- Batch size configuration
- Worker thread count
- Mixed precision training

**Memory Management:**
- Cache size limits
- Memory monitoring
- Auto-cleanup thresholds
- Frame cache limits

## 💻 Command Line Interface

### Basic Usage

```bash
# Simple video search
python main.py --video video.mp4 --query "red car"

# With custom settings
python main.py --video video.mp4 --query "person walking" \
  --threshold 0.25 --max-results 10 --create-clips --create-thumbnails
```

### Complete CLI Reference

#### Video Search Options

```bash
--video PATH              # Path to video file
--query TEXT              # Search query
--threshold FLOAT         # Similarity threshold (0.1-0.5)
--max-results INT         # Maximum number of results
--frame-interval INT      # Frame sampling interval
--resolution WIDTHxHEIGHT # Processing resolution
--quality FLOAT           # Compression quality (0.1-1.0)
--create-clips            # Generate video clips
--create-thumbnails       # Extract thumbnail images
--clip-duration FLOAT     # Duration of video clips (seconds)
--output-dir PATH         # Output directory
```

#### Live Detection Options

```bash
--live QUERY              # Start live detection with query
--camera INT              # Camera index (default: 0)
--stream URL              # RTSP stream URL
--detection-interval FLOAT # Detection interval (seconds)
--max-detections INT      # Maximum detections to keep
--confidence FLOAT        # Detection confidence threshold
```

#### Advanced Options

```bash
--clip-model NAME         # CLIP model to use
--device DEVICE           # Processing device (cpu/cuda/mps)
--batch-size INT          # Processing batch size
--workers INT             # Number of worker threads
--enable-gpu              # Enable GPU acceleration
--advanced-matching       # Use advanced matching algorithms
--config PATH             # Configuration file path
--verbose                 # Enable verbose logging
--quiet                   # Suppress output
```

### Example Commands

```bash
# High-quality search with clips
python main.py --video movie.mp4 --query "car chase" \
  --threshold 0.3 --create-clips --clip-duration 5.0

# Fast thumbnail extraction
python main.py --video video.mp4 --query "sunset" \
  --frame-interval 60 --create-thumbnails --resolution 512x384

# Live detection with IP camera
python main.py --live "person" \
  --stream "rtsp://*************:554/stream" \
  --confidence 0.7 --max-detections 20

# Batch processing multiple videos
python main.py --batch-videos video1.mp4 video2.mp4 video3.mp4 \
  --query "dog" --threshold 0.2

# Using specific CLIP model
python main.py --video video.mp4 --query "object" \
  --clip-model "openai/clip-vit-large-patch14" --enable-gpu
```

## 📹 Live Video Detection

### Supported Sources

1. **Webcams and USB Cameras**
   ```bash
   python main.py --live "person" --camera 0
   ```

2. **RTSP Streams**
   ```bash
   python main.py --live "vehicle" --stream "rtsp://camera-ip/stream"
   ```

3. **IP Cameras**
   ```bash
   python main.py --live "motion" --stream "http://camera-ip/mjpeg"
   ```

4. **YouTube Live Streams** (experimental)
   ```bash
   python main.py --live "event" --stream "https://youtube.com/watch?v=LIVE_ID"
   ```

### Live Detection Features

- **Real-time Processing:** Analyze video streams in real-time
- **Pause/Resume:** Control detection without losing session
- **Configurable Intervals:** Adjust detection frequency
- **Result Limiting:** Set maximum number of detections to keep
- **Confidence Filtering:** Filter results by detection confidence
- **Auto-save:** Automatically save detected images

### Performance Tips for Live Detection

- Use lower resolution for faster processing
- Increase detection interval for better performance
- Enable GPU acceleration
- Limit maximum detections to prevent memory issues
- Use specific queries for better accuracy

## 📊 Batch Processing

### Multiple Videos, One Query

**Use Cases:**
- Analyze multiple security camera recordings
- Search through a video library
- Compare content across different videos

**Steps:**
1. Upload multiple video files
2. Enter a single search query
3. Configure processing settings
4. Start batch processing
5. Compare results across videos

### One Video, Multiple Queries

**Use Cases:**
- Comprehensive content analysis
- Multi-object detection
- Scene categorization

**Steps:**
1. Upload one video file
2. Add multiple search queries
3. Configure processing settings
4. Start batch processing
5. Analyze comprehensive results

### Batch Processing Tips

- Use consistent settings across all videos
- Monitor memory usage during processing
- Save results for later analysis
- Use lower thresholds for exploratory analysis
- Enable parallel processing for speed

## 🔧 Advanced Features

### Object Extraction and Isolation

The application can extract and display only the detected objects, not the full video frames:

**Features:**
- YOLO-based object detection
- Precise object cropping
- Background removal
- Multiple object extraction per frame
- Confidence-based filtering

**Configuration:**
```python
# In advanced settings
enable_object_extraction = True
confidence_threshold = 0.5
max_objects_per_frame = 5
```

### Advanced Matching Algorithms

Enhanced matching for better accuracy:

**Features:**
- Semantic filtering
- Temporal consistency checking
- Result clustering
- Multi-criteria ranking
- False positive reduction

**Usage:**
```bash
python main.py --video video.mp4 --query "object" --advanced-matching
```

### Memory Management

Intelligent memory handling for large videos:

**Features:**
- LRU caching
- Automatic cleanup
- Memory monitoring
- Chunked processing
- Garbage collection

### GPU Acceleration

Support for multiple GPU types:

**NVIDIA CUDA:**
```bash
# Automatic detection
python main.py --video video.mp4 --query "object" --enable-gpu
```

**AMD ROCm:**
```bash
# Set device explicitly
python main.py --video video.mp4 --query "object" --device rocm
```

**Apple Silicon (MPS):**
```bash
# Automatic detection on Apple Silicon Macs
python main.py --video video.mp4 --query "object" --device mps
```

## ⚙️ Configuration & Settings

### Configuration Files

The application uses multiple configuration systems:

1. **Basic Configuration** (`config.py`)
2. **Advanced Configuration** (`config_advanced.py`)
3. **User Settings** (`config.json`)

### Key Settings

**Model Settings:**
- `clip_model_name`: CLIP model to use
- `yolo_model_name`: YOLO model for object detection
- `device`: Processing device (auto/cpu/cuda/mps)

**Processing Settings:**
- `frame_interval`: Frame sampling rate
- `target_resolution`: Processing resolution
- `quality_factor`: Compression quality
- `chunk_size`: Frames per processing chunk

**Performance Settings:**
- `enable_gpu`: GPU acceleration
- `batch_size`: Processing batch size
- `num_workers`: Worker thread count
- `enable_mixed_precision`: Mixed precision training

**Memory Settings:**
- `max_cache_size_mb`: Maximum cache size
- `enable_memory_monitoring`: Memory usage tracking
- `auto_cleanup_threshold`: Automatic cleanup trigger

### Customizing Settings

**Via Web Interface:**
1. Go to "Advanced Settings" tab
2. Modify desired settings
3. Click "Save Settings"

**Via Configuration File:**
```json
{
  "models": {
    "clip_model_name": "openai/clip-vit-large-patch14",
    "device": "cuda"
  },
  "processing": {
    "frame_interval": 15,
    "target_resolution": [1024, 768]
  },
  "performance": {
    "enable_gpu": true,
    "batch_size": 32
  }
}
```

**Via Command Line:**
```bash
python main.py --video video.mp4 --query "object" \
  --clip-model "openai/clip-vit-large-patch14" \
  --batch-size 32 --enable-gpu
```

## ⚡ Performance Optimization

### Hardware Optimization

**GPU Acceleration:**
- Install appropriate GPU drivers (CUDA/ROCm)
- Use GPU-optimized models
- Enable mixed precision training
- Optimize batch sizes for your GPU memory

**CPU Optimization:**
- Use multiple worker threads
- Enable parallel processing
- Optimize frame intervals
- Use lower processing resolutions

**Memory Optimization:**
- Enable chunked processing for large videos
- Set appropriate cache limits
- Use memory monitoring
- Enable automatic cleanup

### Software Optimization

**Model Selection:**
- Use smaller models for speed (clip-vit-base-patch32)
- Use larger models for accuracy (clip-vit-large-patch14)
- Choose appropriate YOLO models (yolov8n for speed, yolov8x for accuracy)

**Processing Settings:**
- Adjust frame intervals based on video content
- Use adaptive configuration
- Enable advanced matching selectively
- Optimize resolution for your use case

### Performance Benchmarks

**Typical Performance (with GPU):**
- **Small Videos** (< 1GB): 30-60 FPS processing
- **Medium Videos** (1-5GB): 15-30 FPS processing
- **Large Videos** (> 5GB): 5-15 FPS processing

**Memory Usage:**
- **Base Application**: 1-2GB RAM
- **Small Videos**: +1-2GB RAM
- **Large Videos**: +3-8GB RAM (with chunking)

## 🐛 Troubleshooting

### Common Issues and Solutions

#### Installation Issues

**Problem:** `pip install` fails with dependency conflicts
**Solution:**
```bash
# Create fresh virtual environment
python -m venv fresh_env
source fresh_env/bin/activate  # Linux/macOS
fresh_env\Scripts\activate     # Windows
pip install -r requirements.txt
```

**Problem:** CUDA out of memory
**Solution:**
- Reduce batch size: `--batch-size 8`
- Lower resolution: `--resolution 512x384`
- Enable chunked processing
- Close other GPU applications

**Problem:** Slow processing on CPU
**Solution:**
- Install GPU drivers and enable GPU acceleration
- Reduce frame interval: `--frame-interval 60`
- Use lower resolution: `--resolution 256x192`
- Enable parallel processing

#### Runtime Issues

**Problem:** No matches found
**Solution:**
- Lower similarity threshold: `--threshold 0.1`
- Try different query phrasings
- Check video quality and content
- Enable advanced matching

**Problem:** Web interface won't start
**Solution:**
```bash
# Check if port is in use
netstat -an | grep 8501

# Use different port
streamlit run app/interface.py --server.port 8502
```

**Problem:** Live detection fails
**Solution:**
- Check camera permissions
- Verify camera index: `--camera 1`
- Test with different resolution
- Check RTSP stream URL format

#### Performance Issues

**Problem:** High memory usage
**Solution:**
- Enable memory monitoring
- Reduce cache size
- Use chunked processing
- Lower frame intervals

**Problem:** Slow search results
**Solution:**
- Enable GPU acceleration
- Increase frame interval
- Use smaller CLIP model
- Reduce processing resolution

### Diagnostic Commands

```bash
# Test installation
python test_application.py

# Check GPU availability
python -c "import torch; print(torch.cuda.is_available())"

# Memory usage monitoring
python -c "import psutil; print(f'RAM: {psutil.virtual_memory().percent}%')"

# Model loading test
python -c "from models.clip_model import CLIPMatcher; CLIPMatcher()"
```

### Getting Help

1. **Check Logs:** Look in `logs/app.log` for detailed error messages
2. **Run Diagnostics:** Use `python test_application.py`
3. **Check Documentation:** Review relevant guide sections
4. **Search Issues:** Look for similar problems in GitHub issues
5. **Create Issue:** Provide detailed error information and system specs

## 💡 Tips & Best Practices

### Search Query Optimization

**Effective Queries:**
- Be specific: "red sports car" vs "car"
- Use descriptive language: "person walking dog" vs "person"
- Include context: "sunset over ocean" vs "sunset"
- Try variations: "automobile", "vehicle", "car"

**Query Examples by Category:**

**Objects:**
- "red car", "blue bicycle", "white truck"
- "laptop computer", "mobile phone", "coffee cup"
- "wooden chair", "glass table", "metal fence"

**People & Actions:**
- "person walking", "child running", "woman sitting"
- "man in suit", "person with backpack", "group of people"
- "person on bicycle", "someone cooking", "people dancing"

**Scenes & Environments:**
- "outdoor scene", "indoor room", "city street"
- "beach sunset", "mountain landscape", "forest path"
- "crowded place", "empty room", "busy intersection"

**Animals:**
- "dog running", "cat sleeping", "bird flying"
- "horse in field", "fish swimming", "butterfly on flower"

### Video Preparation

**Optimal Video Formats:**
- **MP4** with H.264 encoding (best compatibility)
- **Resolution:** 720p-1080p (good balance of quality/speed)
- **Frame Rate:** 24-30 FPS (standard rates)
- **Bitrate:** 2-8 Mbps (good quality without excessive size)

**Video Quality Tips:**
- Ensure good lighting in source videos
- Avoid heavily compressed or low-quality videos
- Use stable camera footage when possible
- Consider video length vs processing time trade-offs

### Performance Optimization Tips

**For Speed:**
- Use frame intervals of 30-60 for fast processing
- Choose lower resolutions (512x384 or 256x192)
- Enable GPU acceleration
- Use smaller CLIP models
- Disable video clip generation, use thumbnails only

**For Accuracy:**
- Use frame intervals of 10-30 for thorough analysis
- Choose higher resolutions (1024x768 or higher)
- Use larger CLIP models
- Enable advanced matching
- Use lower similarity thresholds

**For Large Videos:**
- Enable chunked processing
- Increase frame intervals
- Monitor memory usage
- Use lower processing resolutions
- Consider splitting very large videos

### Workflow Recommendations

**Content Discovery Workflow:**
1. Start with broad queries and low thresholds
2. Review results to understand video content
3. Refine queries based on initial findings
4. Use higher thresholds for final precise results

**Security/Surveillance Workflow:**
1. Use live detection for real-time monitoring
2. Set appropriate confidence thresholds
3. Configure automatic saving of detections
4. Review saved results periodically

**Content Analysis Workflow:**
1. Use batch processing for multiple videos
2. Apply consistent settings across all videos
3. Export results for further analysis
4. Use analytics features to identify patterns

### Best Practices Summary

1. **Start Simple:** Begin with basic queries and default settings
2. **Iterate:** Refine queries and settings based on results
3. **Monitor Performance:** Keep an eye on processing times and memory usage
4. **Save Configurations:** Save successful settings for reuse
5. **Regular Updates:** Keep the application and models updated
6. **Backup Results:** Save important search results and configurations
7. **Document Workflows:** Keep notes on effective query patterns and settings

---

This completes the comprehensive user guide. For additional help, consult the other documentation files or create an issue on GitHub with your specific questions.
