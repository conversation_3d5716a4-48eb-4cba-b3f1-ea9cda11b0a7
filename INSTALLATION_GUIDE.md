# 🚀 AI-Powered Video Content Search - Complete Installation Guide

This comprehensive guide provides multiple installation methods for the AI-Powered Video Content Search application, from simple automated installation to advanced Docker deployments.

## 📋 Table of Contents

1. [System Requirements](#system-requirements)
2. [Quick Installation](#quick-installation)
3. [Manual Installation](#manual-installation)
4. [Docker Installation](#docker-installation)
5. [Advanced Installation](#advanced-installation)
6. [Testing Installation](#testing-installation)
7. [Troubleshooting](#troubleshooting)
8. [Post-Installation Setup](#post-installation-setup)

## 🖥️ System Requirements

### Minimum Requirements
- **Operating System:** Windows 10+, macOS 10.15+, or Linux (Ubuntu 18.04+, CentOS 7+)
- **Python:** 3.8 or higher (3.10+ recommended)
- **Memory:** 4GB RAM (8GB+ recommended)
- **Storage:** 10GB free disk space
- **Internet:** Broadband connection for model downloads

### Recommended Requirements
- **Memory:** 16GB+ RAM for large video processing
- **GPU:** NVIDIA GPU with 4GB+ VRAM (for acceleration)
- **Storage:** SSD with 20GB+ free space
- **CPU:** Multi-core processor (4+ cores recommended)

### System Dependencies
- **Git:** Version control system
- **FFmpeg:** Multimedia framework for video processing

## 🚀 Quick Installation

### Option 1: Automated Installation (Recommended)

#### Windows
```batch
# Download and run the automated installer
git clone https://github.com/yourusername/ai-video-search.git
cd ai-video-search
install.bat
```

#### Linux/macOS
```bash
# Download and run the automated installer
git clone https://github.com/yourusername/ai-video-search.git
cd ai-video-search
chmod +x install.sh
./install.sh
```

### Option 2: Advanced Installation Manager
```bash
# Use the advanced installation manager for more options
python install_manager.py
```

### Option 3: Requirements Manager
```bash
# Generate optimized requirements for your system
python requirements_manager.py --create-all
pip install -r requirements.txt
```

## 🔧 Manual Installation

### Step 1: Clone Repository
```bash
git clone https://github.com/yourusername/ai-video-search.git
cd ai-video-search
```

### Step 2: Create Virtual Environment (Recommended)
```bash
# Create virtual environment
python -m venv venv

# Activate virtual environment
# Windows:
venv\Scripts\activate
# Linux/macOS:
source venv/bin/activate
```

### Step 3: Install System Dependencies

#### Windows
```batch
# Using Chocolatey (recommended)
choco install git ffmpeg

# Or using Scoop
scoop install git ffmpeg

# Or download manually:
# Git: https://git-scm.com/download/win
# FFmpeg: https://ffmpeg.org/download.html
```

#### macOS
```bash
# Using Homebrew (recommended)
brew install git ffmpeg

# Or using MacPorts
sudo port install git ffmpeg
```

#### Linux
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install git ffmpeg

# CentOS/RHEL
sudo yum install git ffmpeg

# Arch Linux
sudo pacman -S git ffmpeg
```

### Step 4: Install Python Dependencies
```bash
# Upgrade pip
pip install --upgrade pip

# Install requirements
pip install -r requirements.txt
```

### Step 5: Create Configuration
```bash
# Run installation script to create config
python install.py
```

### Step 6: Test Installation
```bash
# Run comprehensive tests
python test_installation.py
```

## 🐳 Docker Installation

### Prerequisites
- Docker 20.10+ installed
- Docker Compose 2.0+ installed
- 8GB+ RAM available for containers

### Quick Docker Setup
```bash
# Clone repository
git clone https://github.com/yourusername/ai-video-search.git
cd ai-video-search

# Run automated Docker installation
chmod +x docker-install.sh
./docker-install.sh
```

### Manual Docker Setup

#### Step 1: Configure Environment
```bash
# Copy environment template
cp .env.example .env

# Edit configuration (optional)
nano .env
```

#### Step 2: Build and Start Services
```bash
# Basic installation (web interface only)
docker-compose up -d

# With Redis cache
docker-compose --profile with-redis up -d

# Full production setup
docker-compose --profile with-redis --profile with-database --profile with-nginx --profile with-monitoring up -d
```

#### Step 3: Access Application
- **Web Interface:** http://localhost:8501
- **Monitoring:** http://localhost:3000 (if enabled)

### Docker GPU Support (NVIDIA)
```bash
# Install NVIDIA Container Toolkit first
# Then enable GPU in .env:
echo "INSTALL_GPU=true" >> .env

# Uncomment GPU section in docker-compose.yml
# Then restart services
docker-compose down
docker-compose up -d
```

## 🔬 Advanced Installation

### Custom CLIP Models
```bash
# Install with specific CLIP model
python requirements_manager.py --gpu --optional advanced_ml
pip install -r requirements.txt

# Configure in config.json
{
  "models": {
    "clip_model_name": "openai/clip-vit-large-patch14"
  }
}
```

### Development Installation
```bash
# Install development dependencies
python requirements_manager.py --dev --gpu
pip install -r requirements-dev.txt

# Install in development mode
pip install -e .

# Setup pre-commit hooks
pre-commit install
```

### Cloud Deployment
```bash
# Install cloud dependencies
python requirements_manager.py --optional cloud api monitoring
pip install -r requirements-cloud.txt

# Configure cloud storage (AWS example)
export AWS_ACCESS_KEY_ID=your_key
export AWS_SECRET_ACCESS_KEY=your_secret
export S3_BUCKET=your-bucket
```

## 🧪 Testing Installation

### Comprehensive Testing
```bash
# Run full test suite
python test_installation.py

# Run specific tests
python -m pytest tests/ -v

# Performance benchmarking
python tests/benchmark_performance.py
```

### Quick Verification
```bash
# Test basic functionality
python -c "from utils.clip_match import VideoSearchEngine; print('✅ Installation successful!')"

# Test web interface
python main.py --web &
curl -f http://localhost:8501/_stcore/health
```

### Docker Testing
```bash
# Test Docker installation
docker-compose exec ai-video-search python test_installation.py

# Check service health
docker-compose ps
docker-compose logs ai-video-search
```

## 🐛 Troubleshooting

### Common Issues

#### Python/Pip Issues
```bash
# Python not found
# Solution: Add Python to PATH or use full path

# pip install fails
# Solution: Upgrade pip and use virtual environment
python -m pip install --upgrade pip
python -m venv fresh_env
source fresh_env/bin/activate  # Linux/macOS
fresh_env\Scripts\activate     # Windows
```

#### GPU Issues
```bash
# CUDA out of memory
# Solution: Reduce batch size or use CPU
export CUDA_VISIBLE_DEVICES=""  # Force CPU usage

# NVIDIA driver issues
# Solution: Update drivers and install CUDA toolkit
nvidia-smi  # Check driver status
```

#### System Dependencies
```bash
# FFmpeg not found
# Windows: choco install ffmpeg
# macOS: brew install ffmpeg
# Linux: sudo apt install ffmpeg

# Git not found
# Download from: https://git-scm.com/
```

#### Docker Issues
```bash
# Docker not running
sudo systemctl start docker  # Linux
# Start Docker Desktop on Windows/macOS

# Permission denied
sudo usermod -aG docker $USER  # Linux
# Restart terminal after adding to docker group

# Port already in use
docker-compose down  # Stop existing containers
netstat -tulpn | grep 8501  # Check what's using the port
```

### Performance Issues
```bash
# Slow processing
# Solutions:
# 1. Enable GPU acceleration
# 2. Reduce frame interval
# 3. Use lower resolution
# 4. Increase available memory

# Memory issues
# Solutions:
# 1. Close other applications
# 2. Use chunked processing
# 3. Reduce batch size
# 4. Enable memory monitoring
```

### Getting Help
1. **Check logs:** `logs/app.log` or `docker-compose logs`
2. **Run diagnostics:** `python test_installation.py`
3. **Check documentation:** Review relevant sections
4. **Search issues:** GitHub issues page
5. **Create issue:** Provide system info and error logs

## ⚙️ Post-Installation Setup

### Initial Configuration
```bash
# Create initial config
python -c "from config_advanced import AdvancedConfig; AdvancedConfig().save_config()"

# Set up directories
mkdir -p videos data/models static/output_clips
```

### First Run
```bash
# Start web interface
python main.py --web
# Open http://localhost:8501

# Test command line
python main.py --video sample.mp4 --query "test"

# Test live detection
python main.py --live --query "person"
```

### Performance Optimization
```bash
# Enable GPU (if available)
python -c "import torch; print('CUDA available:', torch.cuda.is_available())"

# Configure memory limits
# Edit config.json:
{
  "memory": {
    "max_cache_size_mb": 4096,
    "enable_memory_monitoring": true
  }
}
```

### Security Setup (Production)
```bash
# Change default passwords (Docker)
# Edit .env file:
POSTGRES_PASSWORD=secure_password
GRAFANA_PASSWORD=secure_password

# Enable authentication (if implementing)
# Configure in config.json
```

## 🎯 Next Steps

After successful installation:

1. **📖 Read the User Guide:** `docs/COMPLETE_USER_GUIDE.md`
2. **🎬 Try Sample Videos:** Place videos in `videos/` directory
3. **⚙️ Configure Settings:** Customize `config.json` for your needs
4. **📊 Monitor Performance:** Use built-in analytics
5. **🔄 Keep Updated:** Regularly update dependencies

## 📞 Support

- **Documentation:** `docs/` directory
- **API Reference:** `docs/API_REFERENCE.md`
- **GitHub Issues:** Report bugs and request features
- **Community:** Join discussions and get help

---

**🎉 Congratulations! You're ready to start using AI-Powered Video Content Search!**
